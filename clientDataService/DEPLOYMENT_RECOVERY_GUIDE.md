# 🚨 Deployment Recovery Guide

This guide helps you recover from common deployment failures and UPDATE_ROLLBACK_FAILED states.

## Quick Recovery Commands

### Check Stack Status
```bash
npm run stack:status
# or
aws cloudformation describe-stacks --stack-name cds-service-dev --profile atomize-dev --region us-east-2 --query 'Stacks[0].StackStatus' --output text
```

### Safe Deployment (Recommended)
```bash
npm run deploy:safe
# or for other stages
npm run deploy:safe:staging
npm run deploy:safe:prod
```

### Emergency Stack Recovery
```bash
# 1. Delete corrupted stack
npm run stack:delete

# 2. Wait for deletion (5-15 minutes)
aws cloudformation wait stack-delete-complete --stack-name cds-service-dev --profile atomize-dev --region us-east-2

# 3. Fresh deployment
npm run deploy:safe
```

## Common Stack States & Solutions

### ✅ Healthy States
- `CREATE_COMPLETE` - Ready for updates
- `UPDATE_COMPLETE` - Ready for updates
- `STACK_NOT_EXISTS` - Ready for initial deployment

### ⚠️ Warning States
- `UPDATE_ROLLBACK_COMPLETE` - Previous deployment failed but stack is stable
  - **Solution**: Investigate logs, then deploy normally

### ❌ Failed States
- `UPDATE_ROLLBACK_FAILED` - Stack is corrupted
  - **Solution**: Delete and recreate stack
- `UPDATE_FAILED` - Deployment failed
  - **Solution**: Check logs, fix issues, redeploy
- `CREATE_FAILED` - Initial deployment failed
  - **Solution**: Delete stack and redeploy

### 🔄 In-Progress States
- `UPDATE_IN_PROGRESS` - Wait for completion
- `CREATE_IN_PROGRESS` - Wait for completion
- `DELETE_IN_PROGRESS` - Wait for completion

## Troubleshooting Steps

### 1. Pre-Deployment Checks
```bash
npm run pre-deploy-check
```

### 2. View Recent Stack Events
```bash
npm run stack:events
```

### 3. Force Deployment (Skip Checks)
```bash
npm run deploy:force
```

### 4. Manual CloudFormation Commands

#### Continue Failed Rollback
```bash
aws cloudformation continue-update-rollback --stack-name cds-service-dev --profile atomize-dev --region us-east-2
```

#### Skip Problematic Resources
```bash
aws cloudformation continue-update-rollback \
  --stack-name cds-service-dev \
  --profile atomize-dev \
  --region us-east-2 \
  --resources-to-skip ResourceName1 ResourceName2
```

## Prevention Best Practices

### 1. Always Run Pre-Checks
```bash
npm run pre-deploy-check
```

### 2. Use Safe Deployment
```bash
npm run deploy:safe
```

### 3. Monitor Stack Status
```bash
npm run stack:status
```

### 4. Regular Backups
- Deployment backups are automatically created in `./deployment-backups/`
- Keep backups of working configurations

## Environment Differences

### Why Your Senior Developer Succeeds
1. **Clean Stack State** - No corrupted resources
2. **Consistent Environment** - Matching local setup
3. **No Resource Conflicts** - Fresh deployment state

### Ensuring Consistency
1. Use the same AWS profile: `atomize-dev`
2. Use the same region: `us-east-2`
3. Use the same Node.js version
4. Run `npm install` before deployment

## Emergency Contacts

If all else fails:
1. Check with your senior developer for their exact deployment process
2. Compare your local environment with theirs
3. Consider pair programming the next deployment

## Useful AWS CLI Commands

```bash
# List all stacks
aws cloudformation list-stacks --profile atomize-dev --region us-east-2

# Get stack template
aws cloudformation get-template --stack-name cds-service-dev --profile atomize-dev --region us-east-2

# Validate template
aws cloudformation validate-template --template-body file://path/to/template.json --profile atomize-dev --region us-east-2
```

---

**Remember**: When in doubt, delete and recreate the stack. It's safer than trying to fix a corrupted state.
