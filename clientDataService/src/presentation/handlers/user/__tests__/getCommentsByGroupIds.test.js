import { handler } from '../getCommentsByGroupId.js';

// Mock da conexão com MongoDB
jest.mock('../../../../infra/libs/mongodb/connect.js', () => jest.fn());

// Mock dos modelos
jest.mock('../../../../modules/user/useCases/getCommentsByGroupId.js', () => ({
  getCommentsByGroupIds: jest.fn()
}));

import { getCommentsByGroupIds } from '../../../../modules/user/useCases/getCommentsByGroupId.js';

describe('GetCommentsByGroupIds Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should handle multiple group IDs from path parameter', async () => {
    const mockResult = {
      comments: [
        {
          commentId: 'comment1',
          userId: 'user1',
          videoId: 123,
          user: { name: 'Test User', email: '<EMAIL>' },
          isDoubt: true
        }
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrevious: false
      },
      groupsInfo: [
        { groupId: 'group1', groupName: 'Test Group 1', membersCount: 5 },
        { groupId: 'group2', groupName: 'Test Group 2', membersCount: 3 }
      ],
      summary: {
        totalGroups: 2,
        totalMembers: 8,
        commentsFound: 1
      }
    };

    getCommentsByGroupIds.mockResolvedValue(mockResult);

    const event = {
      pathParameters: {
        groupIds: 'group1,group2'
      },
      queryStringParameters: {
        isDoubt: 'true',
        page: '1',
        limit: '20'
      }
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(getCommentsByGroupIds).toHaveBeenCalledWith({
      groupIds: ['group1', 'group2'],
      videoId: null,
      isDoubt: true,
      page: 1,
      limit: 20
    });

    const body = JSON.parse(result.body);
    expect(body.body.comments).toHaveLength(1);
    expect(body.body.summary.totalGroups).toBe(2);
  });

  test('should handle group IDs from request body', async () => {
    const mockResult = {
      comments: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false
      },
      groupsInfo: [],
      summary: {
        totalGroups: 0,
        totalMembers: 0,
        commentsFound: 0
      }
    };

    getCommentsByGroupIds.mockResolvedValue(mockResult);

    const event = {
      pathParameters: {},
      queryStringParameters: {},
      body: JSON.stringify({
        groupIds: ['group1', 'group2', 'group3']
      })
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(getCommentsByGroupIds).toHaveBeenCalledWith({
      groupIds: ['group1', 'group2', 'group3'],
      videoId: null,
      isDoubt: false,
      page: 1,
      limit: 20
    });
  });

  test('should handle optional videoId parameter', async () => {
    const mockResult = {
      comments: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false
      },
      groupsInfo: [],
      summary: {
        totalGroups: 0,
        totalMembers: 0,
        commentsFound: 0
      }
    };

    getCommentsByGroupIds.mockResolvedValue(mockResult);

    const event = {
      pathParameters: {
        groupIds: 'group1'
      },
      queryStringParameters: {
        videoId: '12345',
        isDoubt: 'false'
      }
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(200);
    expect(getCommentsByGroupIds).toHaveBeenCalledWith({
      groupIds: ['group1'],
      videoId: '12345',
      isDoubt: false,
      page: 1,
      limit: 20
    });
  });

  test('should handle validation errors', async () => {
    const error = new Error('groupIds array is required and cannot be empty');
    error.name = 'ValidationError';
    
    getCommentsByGroupIds.mockRejectedValue(error);

    const event = {
      pathParameters: {},
      queryStringParameters: {}
    };

    const result = await handler(event);

    expect(result.statusCode).toBe(500); // Como não temos CustomError implementado, vai ser 500
    const body = JSON.parse(result.body);
    expect(body.body.message).toContain('groupIds array is required');
  });
});
