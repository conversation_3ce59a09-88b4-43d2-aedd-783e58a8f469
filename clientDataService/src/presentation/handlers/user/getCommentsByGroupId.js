import { getCommentsByGroupId } from '../../../modules/user/useCases/getCommentsByGroupId.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';
import { CustomError } from '../../../utils/customErrors/index.js';

export async function handler(event) {
    try {
        await connectToDB();

        // Get parameters from path and query string
        const { groupId } = event.pathParameters || {};
        const {
            videoId,
            isDoubt,
            page,
            limit
        } = event.queryStringParameters || {};

        const { id: userId } = event.requestContext.authorizer || {};
        if (!userId) {
            throw new AuthorizationError('Authentication required');
        }

        console.log('Getting comments by group ID:', {
            groupId,
            videoId,
            userId,
            isDoubt,
            page,
            limit
        });

        const result = await getCommentsByGroupId({
            groupId,
            videoId: videoId || null,
            isDoubt: isDoubt === 'true',
            page: page ? parseInt(page) : 1,
            limit: limit ? parseInt(limit) : 20,
            userId
        });

        return apiResponse(200, { body: result });

    } catch (error) {
        console.error('Error getting comments by group ID:', error);

        if (error instanceof CustomError) {
            return apiResponse(error.code, { body: { message: error.message } });
        }

        return apiResponse(500, { body: { message: error.message } });
    }
}