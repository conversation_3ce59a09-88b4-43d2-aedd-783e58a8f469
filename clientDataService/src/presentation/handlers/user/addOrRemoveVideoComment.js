import addOrRemoveVideoCommentMetricsService from '../../../application/services/lessonMetrics/addOrRemoveVideoCommentMetricsService.js';
import { CustomError } from "../../../utils/customErrors/index.js";
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();
    const body = JSON.parse(event.body);
    const { id, videoId, commentId, isDoubt, isRemove } = body;
    console.log("Request body:", body);

    const updatedData = await addOrRemoveVideoCommentMetricsService({
      userId: id,
      videoId,
      commentId,
      isDoubt,
      isRemove
    });

    return apiResponse(200, { body: updatedData });
  } catch (error) {
    console.error("Error updating video comment:", error);

    if (error instanceof CustomError) {
      return apiResponse(error.code, { body: { message: error.message } });
    }

    return apiResponse(500, { body: { message: error.message } });
  }
} 