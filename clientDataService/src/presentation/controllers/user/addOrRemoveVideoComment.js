import addOrRemoveVideoCommentMetricsService from "../../../application/services/lessonMetrics/addOrRemoveVideoCommentMetricsService.js";
import { CustomError } from "../../../utils/customErrors/index.js";

export default async function addOrRemoveVideoCommentController(req, res) {
    const { id, videoId, commentId, isDoubt } = req.body;

    try {
        const updatedVideo = await addOrRemoveVideoCommentMetricsService({ userId: id, videoId, commentId, isDoubt });
        return res.status(200).send(updatedVideo);
    } catch (error) {
        console.log(error);
        if (error instanceof CustomError)
            return res.status(error.code).send(error.message);
        return res.status(500).send(error.message);
    }
}