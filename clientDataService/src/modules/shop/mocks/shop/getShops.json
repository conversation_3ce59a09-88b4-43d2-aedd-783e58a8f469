{"resource": "/shop/shops", "path": "/shop/shops", "httpMethod": "GET", "headers": {"Content-Type": "application/json", "Authorization": "Bearer mock-jwt-token"}, "queryStringParameters": null, "pathParameters": null, "stageVariables": null, "requestContext": {"resourceId": "mock-resource-id", "resourcePath": "/shop/shops", "httpMethod": "GET", "extendedRequestId": "mock-extended-request-id", "requestTime": "01/Jan/2024:00:00:00 +0000", "path": "/shop/shops", "accountId": "mock-account-id", "protocol": "HTTP/1.1", "stage": "dev", "domainPrefix": "mock-domain", "requestTimeEpoch": *************, "requestId": "mock-request-id", "identity": {"cognitoIdentityPoolId": null, "accountId": null, "cognitoIdentityId": null, "caller": null, "sourceIp": "127.0.0.1", "principalOrgId": null, "accessKey": null, "cognitoAuthenticationType": null, "cognitoAuthenticationProvider": null, "userArn": null, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "user": null}, "domainName": "mock-api-gateway.amazonaws.com", "apiId": "mock-api-id", "authorizer": {"id": "67e4307b4430f6d8fb52dc58", "email": "<EMAIL>", "name": "Test User"}}, "body": null, "isBase64Encoded": false}