import { inventoryUseCases } from '../../useCases/index.js';
import { ValidationError, AuthorizationError } from "../../../../utils/customErrors/index.js";

export const handler = async (event) => {
    if (event.source === "serverless-plugin-warmup") {
        return "Lambda warmed up!";
    }

    // Get user ID and role from authorizer
    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
        throw new ValidationError('User ID is required');
    }

    // Check if user is admin
    if (role !== 'admin') {
        throw new AuthorizationError('Only admins can access this endpoint');
    }

    // Parse request body
    const requestBody = JSON.parse(event.body || '{}');
    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM-HANDLER] Request body:', JSON.stringify(requestBody, null, 2));
    
    // Extract required parameters
    const { targetOwnerId, itemId, targetStatus, metadata = {}, order = null } = requestBody;
    
    if (!targetOwnerId) {
        throw new ValidationError('Target Owner ID is required');
    }
    
    if (!itemId) {
        throw new ValidationError('Item ID is required');
    }
    
    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM-HANDLER] Moving item:', itemId, 'for user:', targetOwnerId, 'to status:', targetStatus || 'auto-next');
    
    // Move the item to its next state using admin function
    return await inventoryUseCases.adminMovementInventoryItem(userId, targetOwnerId, itemId, targetStatus, metadata, order);
};
