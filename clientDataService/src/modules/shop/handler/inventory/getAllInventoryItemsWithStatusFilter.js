import { inventoryUseCases } from '../../useCases/index.js';
import { ValidationError, AuthorizationError } from "../../../../utils/customErrors/index.js";

export const handler = async (event) => {
    if (event.source === "serverless-plugin-warmup") {
        return "Lambda warmed up!";
    }

    // Get user ID and role from authorizer
    const { id: userId, role } = event.requestContext.authorizer || {};
    
    // Check if user has admin privileges
    if (role !== 'admin') {
        throw new AuthorizationError('Only admins can access this endpoint');
    }

    if (!userId) {
        throw new ValidationError('User ID is required');
    }

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    
    // Build filter object from query parameters
    const filter = {};
    
    // Required parameter
    if (!queryParams.status) {
        throw new ValidationError('Status parameter is required');
    }
    filter.status = queryParams.status;
    
    // Optional parameters
    if (queryParams.contractId !== undefined) {
        // Handle 'null' string as actual null for global shop items
        filter.contractId = queryParams.contractId === 'null' ? null : queryParams.contractId;
    }
    
    if (queryParams.itemType) {
        filter.itemType = queryParams.itemType;
    }
    
    if (queryParams.usageType) {
        filter.usageType = queryParams.usageType;
    }
    
    if (queryParams.tags) {
        filter.tags = queryParams.tags.split(',');
    }
    
    if (queryParams.limit) {
        const limit = parseInt(queryParams.limit);
        if (isNaN(limit) || limit < 1) {
            throw new ValidationError('Limit must be a positive number');
        }
        filter.limit = limit;
    }
    
    if (queryParams.page) {
        const page = parseInt(queryParams.page);
        if (isNaN(page) || page < 1) {
            throw new ValidationError('Page must be a positive number');
        }
        filter.page = page;
    }
    
    console.log('[GET-ALL-INVENTORY-ITEMS-WITH-STATUS-FILTER-HANDLER] Filter:', filter);
    
    // Get all inventory items with the provided filter
    return await inventoryUseCases.getAllInventoryItemsWithStatusFilter(filter);
};
