import { InventoryRepository } from '../../repositories/index.js';
import { ValidationError, AuthorizationError } from '../../../../utils/customErrors/index.js';
import { removeProtectedMetadata } from '../../utils/removeProtectedMetadata.js';

/**
 * Gets all inventory items across all users with status filtering (Admin only)
 * @param {Object} filter - Filter criteria
 * @param {string} [filter.status] - Filter by item status (e.g., 'Pending', 'Ready', 'Active')
 * @param {string} [filter.contractId] - Filter by contract ID (if null/undefined, includes global shop items)
 * @param {string} [filter.itemType] - Filter by item type
 * @param {string} [filter.usageType] - Filter by item usage type
 * @param {string[]} [filter.tags] - Filter by item tags
 * @param {number} [filter.limit=50] - Maximum number of items to return
 * @param {number} [filter.page=1] - Page number for pagination
 * @returns {Promise<Object>} Object containing filtered items and pagination info
 */
async function getAllInventoryItemsWithStatusFilter(filter = {}) {
  try {
    // Validate required parameters
    if (!filter.status) {
      throw new ValidationError('Status filter is required');
    }

    // Call repository method to get filtered items
    const result = await InventoryRepository.findAllInventoryItemsWithStatusFilter({
      status: filter.status,
      contractId: filter.contractId,
      itemType: filter.itemType,
      usageType: filter.usageType,
      tags: filter.tags,
      limit: filter.limit,
      page: filter.page
    });

    // Remove protected metadata from items
    result.items = removeProtectedMetadata(result.items);

    return result;

  } catch (error) {
    console.log('[GET-ALL-INVENTORY-ITEMS-WITH-STATUS-FILTER-ERROR]', error.message);
    if (error instanceof ValidationError || error instanceof AuthorizationError) {
      throw error;
    }
    throw new Error(`Error getting all inventory items with status filter: ${error.message}`);
  }
}

export default getAllInventoryItemsWithStatusFilter;
