import { InventoryRepository, ItemRepository } from '../../repositories/index.js';
import { ValidationError, NotFoundError, AuthorizationError } from '../../../../utils/customErrors/index.js';
import { itemUsageTransitionMatrix, statusHierarchy } from '../../utils/moduleConstants.js';
import mongoose from 'mongoose';

/**
 * Admin version: Moves an inventory item to its next state for any user based on its usage type and current status
 * @param {string} adminUserId - The ID of the admin user performing the action
 * @param {string} targetOwnerId - The ID of the user whose inventory contains the item
 * @param {string} itemId - The ID of the item to move
 * @param {string} [targetStatus] - Optional specific target status (if not provided, will auto-determine next state)
 * @param {Object} [metadata] - Optional metadata to add to the item during movement
 * @returns {Promise<Object>} The updated inventory and movement details
 */
async function adminMovementInventoryItem(adminUserId, targetOwnerId, itemId, targetStatus = null, metadata = {}, order = null) {
  const session = await mongoose.startSession();
  session.startTransaction();
  
  try {
    if (!adminUserId) {
      throw new ValidationError('Admin User ID is required');
    }

    if (!targetOwnerId) {
      throw new ValidationError('Target Owner ID is required');
    }

    if (!itemId) {
      throw new ValidationError('Item ID is required');
    }

    // Get the target user's inventory
    const inventory = await InventoryRepository.findByOwnerId({ ownerId: targetOwnerId, isPopulated: true});
    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM] Target Inventory:', JSON.stringify(inventory.items, null, 2));
    if (!inventory) {
      throw new NotFoundError(`Inventory for user ${targetOwnerId} not found`);
    }

    // Find the specific item in the inventory
    const inventoryItem = inventory.items.find(
      item => item.itemInfo.id.toString() === itemId && (order === null || item.order === order)
    );
    if (!inventoryItem) {
      throw new NotFoundError(`Item with ID ${itemId}${order !== null ? ' and order ' + order : ''} not found in target user's inventory`);
    }

    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM] Found inventory item:', {
      itemId: inventoryItem.itemId,
      currentStatus: inventoryItem.status,
      usageType: inventoryItem.itemInfo.usageType,
      quantity: inventoryItem.quantity,
      targetOwnerId: targetOwnerId
    });

    const currentStatus = inventoryItem.status;
    const usageType = inventoryItem.itemInfo.usageType;

    // Get the transition matrix for this usage type
    const transitionMatrix = itemUsageTransitionMatrix[usageType];
    if (!transitionMatrix) {
      throw new ValidationError(`Invalid usage type: ${usageType}`);
    }

    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM] Available transitions for', usageType, ':', Object.keys(transitionMatrix));

    let newStatus;

    if (targetStatus) {
      // Validate that the target status is valid for this usage type
      if (!transitionMatrix.hasOwnProperty(targetStatus)) {
        throw new ValidationError(`Status '${targetStatus}' is not valid for usage type '${usageType}'. Valid statuses: ${Object.keys(transitionMatrix).join(', ')}`);
      }

      // Validate that we can transition from current status to target status
      const currentHierarchy = statusHierarchy[currentStatus];
      const targetHierarchy = statusHierarchy[targetStatus];

      if (targetHierarchy <= currentHierarchy) {
        throw new ValidationError(`Cannot move backwards or stay in same state. Current: ${currentStatus} (${currentHierarchy}), Target: ${targetStatus} (${targetHierarchy})`);
      }

      newStatus = targetStatus;
    } else {
      // Auto-determine the next status based on current status and usage type
      newStatus = getNextStatus(currentStatus, usageType, transitionMatrix);
      
      if (!newStatus) {
        throw new ValidationError(`Item is already in final state '${currentStatus}' for usage type '${usageType}'`);
      }
    }

    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM] Moving from', currentStatus, 'to', newStatus);

    // Start with original metadata and spread the new metadata from input
    const currentItemMetadata = {
      ...(inventoryItem.itemMetadata || {}),
      ...metadata
    };

    // If the item's usageType is 'Redeemable' and we're moving to 'Redeemed' status, set 'redeemed: true' in its metadata
    if (usageType === 'Redeemable' && newStatus === 'Redeemed') {
      currentItemMetadata.redeemed = true;
      const instanceId = inventoryItem._id ? inventoryItem._id.toString() : 'UNKNOWN_INSTANCE_ID';
      console.log(`[ADMIN-MOVEMENT-INVENTORY-ITEM] Item (Definition ID: ${inventoryItem.itemId.toString()}, Instance ID: ${instanceId}) is 'Redeemable' and moving to 'Redeemed' status. Setting its metadata.redeemed = true.`);
    }

    // Add the new movement event to its history
    if (!currentItemMetadata.movementHistory) {
      currentItemMetadata.movementHistory = [];
    }
    const movementMetadata = {
      ...metadata,
      movedAt: new Date(),
      previousStatus: currentStatus,
      newStatus: newStatus,
      usageType: usageType,
      movementType: targetStatus ? 'admin-manual' : 'admin-automatic',
      adminUserId: adminUserId
    };
    currentItemMetadata.movementHistory.push(movementMetadata);

    // Update the item status and metadata in the inventory simultaneously
    const updatedInventory = await InventoryRepository.updateItemStatusAndMetadata({
      ownerId: targetOwnerId,
      itemId: itemId,
      newStatus: newStatus,
      newMetadata: currentItemMetadata,
      session,
      order: order
    });

    await session.commitTransaction();
    session.endSession();

    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM] Successfully moved item to', newStatus);

    return {
      success: true,
      inventory: updatedInventory,
      movement: {
        itemId: itemId,
        itemName: inventoryItem.itemInfo.name,
        previousStatus: currentStatus,
        newStatus: newStatus,
        newMetadata: currentItemMetadata,
        usageType: usageType,
        movementType: targetStatus ? 'admin-manual' : 'admin-automatic',
        movedAt: new Date(),
        metadata: movementMetadata,
        adminUserId: adminUserId,
        targetOwnerId: targetOwnerId
      }
    };
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    console.log('[ADMIN-MOVEMENT-INVENTORY-ITEM-ERROR]', error);

    if (error instanceof ValidationError || error instanceof NotFoundError || error instanceof AuthorizationError) {
      throw error;
    }
    throw new Error(`Error moving inventory item: ${error.message}`);
  }
}

/**
 * Determines the next status for an item based on its current status and usage type
 * @param {string} currentStatus - Current status of the item
 * @param {string} usageType - Usage type of the item
 * @param {Object} transitionMatrix - Transition matrix for the usage type
 * @returns {string|null} Next status or null if already in final state
 */
function getNextStatus(currentStatus, usageType, transitionMatrix) {
  const currentHierarchy = statusHierarchy[currentStatus];
  const validStatuses = Object.keys(transitionMatrix);
  
  // Find the next status in the hierarchy
  let nextStatus = null;
  let nextHierarchy = Infinity;
  
  for (const status of validStatuses) {
    const hierarchy = statusHierarchy[status];
    if (hierarchy > currentHierarchy && hierarchy < nextHierarchy) {
      nextStatus = status;
      nextHierarchy = hierarchy;
    }
  }
  
  return nextStatus;
}

export default adminMovementInventoryItem;
