import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import connectToDB from "../../../../infra/libs/mongodb/connect.js";
import disconnectFromDB from "../../../../infra/libs/mongodb/disconnect.js";

import { handler as createChannelHandler } from "../createChannel.js";
import { handler as updateChannelHandler } from "../updateChannel.js";
import { handler as deleteChannelHandler } from "../deleteChannel.js";
import { handler as getSelfChannelsHandler } from "../getSelfChannels.js";

import { handler as sendMessageHandler } from "../sendMessage.js";
import { handler as editMessageHandler } from "../editMessage.js";
import { handler as deleteMessageHandler } from "../deleteMessage.js";
import { handler as getChannelMessagesHandler } from "../getChannelMessages.js";
import { handler as markMessagesAsViewedHandler } from "../markMessagesAsViewed.js";

import { handler as updateUserPresenceHandler } from "../updateUserPresenceHandler.js";
import { handler as getUnreadMessagesInfoHandler } from "../getUnreadMessagesInfo.js";
import { handler as getUnreadMessagesListHandler } from "../getUnreadMessagesList.js";
import { handler as getOnlineUsersHandler } from "../getOnlineUsers.js";
import { handler as updateLastSeenHandler } from "../updateLastSeen.js";
import { handler as getLastSeenTimestampsHandler } from "../getLastSeenTimestamps.js";
import { handler as addMemberToChannelHandler } from "../addMemberToChannel.js";
import { handler as removeMemberFromChannelHandler } from "../removeMemberFromChannel.js";

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Communication Management Duration");

    // Normalize method and route pattern
    const resource = event.resource;
    const method = event.method || event.httpMethod;

    // Route map: API Gateway resource (pattern) -> method -> handler
    const routes = {
      // Channels
      "/communication/channels": {
        GET: getSelfChannelsHandler,
        POST: createChannelHandler,
      },
      "/communication/channels/{channelId}": {
        PUT: updateChannelHandler,
        DELETE: deleteChannelHandler,
      },
      "/communication/channels/{channelId}/members": {
        POST: addMemberToChannelHandler,
      },
      "/communication/channels/{channelId}/members/{memberUserId}": {
        DELETE: removeMemberFromChannelHandler,
      },

      // Messages
      "/communication/messages": {
        POST: sendMessageHandler,
      },
      "/communication/messages/{messageId}": {
        PUT: editMessageHandler,
        DELETE: deleteMessageHandler,
      },
      "/communication/channels/{channelId}/messages": {
        GET: getChannelMessagesHandler,
      },
      "/communication/channels/{channelId}/mark-viewed": {
        POST: markMessagesAsViewedHandler,
      },

      // Presence and last seen
      "/communication/channels/{channelId}/presence": {
        POST: updateUserPresenceHandler,
      },
      "/communication/channels/{channelId}/last-seen": {
        POST: updateLastSeenHandler,
        GET: getLastSeenTimestampsHandler,
      },

      // Unread and online
      "/communication/unread-messages": {
        GET: getUnreadMessagesInfoHandler,
      },
      "/communication/channels/{channelId}/unread-messages": {
        GET: getUnreadMessagesListHandler,
      },
      "/communication/channels/{channelId}/online-users": {
        GET: getOnlineUsersHandler,
      },
    };

    if (!routes[resource] || !routes[resource][method]) {
      throw new InvalidInputValueError(
        `Interface not mapped! for relative endpoint ${resource} with method : ${method}`
      );
    }

    const response = await routes[resource][method](event);

    console.log("[COMMUNICATION INTERFACE] RESPONSE", { response });
    console.timeEnd("Communication Management Duration");

    return response;
  } catch (error) {
    console.timeEnd("Communication Management Duration");
    console.log("CommunicationManagement error", error?.message);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        error: error.name || "UnexpectedError",
        message: error.message || error,
        formattedMessage: error.formattedMessage || "An unexpected error occurred.",
      })
    );
  } finally {
    await disconnectFromDB();
  }
}
