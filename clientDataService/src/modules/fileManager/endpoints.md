# File Manager Module Endpoints Documentation

This document provides details about all endpoints available in the File Manager module, including their paths, methods, request parameters, and response formats.

## Image Upload Endpoints

### Upload Image

Uploads an image file to S3 storage with automatic path generation and database tracking.

- **Endpoint:** `/fileManager/images/upload`
- **Method:** `POST`
- **Authentication:** Required
- **Content-Type:** `multipart/form-data`
- **Query Parameters:**
  - `TYPE`: Must be "IMAGE" (required)
- **Request Body:**
  - Form field with file data (multipart/form-data)

**Example Request:**
```bash
curl -X POST "https://api.example.com/fileManager/images/upload?TYPE=IMAGE" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg"
```

**Response:**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "data": {
    "id": "64f8a3b2c1d4e5f6a7b8c9d0",
    "fileName": "image.jpg",
    "originalFileName": "image.jpg", 
    "fileType": "IMAGE",
    "mimeType": "image/jpeg",
    "fileSize": 102400,
    "url": "s3://qaas-service-prod-qaas-s3/questions/a1b2c3d/image.jpg",
    "s3Key": "questions/a1b2c3d/image.jpg",
    "uploadedAt": "2023-12-01T10:30:00.000Z",
    "metadata": {
      "userAgent": "Mozilla/5.0...",
      "sourceIp": "***********",
      "uploadMethod": "multipart",
      "uploadedAt": "2023-12-01T10:30:00.000Z",
      "s3Bucket": "qaas-service-prod-qaas-s3"
    }
  }
}
```

## File Storage Structure

Files are stored in S3 with the following path structure:
```
s3://qaas-service-prod-qaas-s3/
  └── questions/
      └── {uuid7digits}/
          └── {sanitized-filename}
```

**Example:** `s3://qaas-service-prod-qaas-s3/questions/a1b2c3d/my_image.jpg`

## File Type Support

### Images (TYPE=IMAGE)
- **Supported formats:** JPEG, JPG, PNG, GIF, WebP, BMP, SVG
- **Max file size:** 10MB
- **MIME types:** `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/bmp`, `image/svg+xml`

### Future Support
- **Documents (TYPE=DOCUMENT):** PDF, Word, Excel, etc. - 50MB limit
- **Videos (TYPE=VIDEO):** MP4, MOV, AVI, etc. - 500MB limit

## Validation Rules

### File Validation
- File must be present in multipart form data
- File type must match declared TYPE parameter
- File size must not exceed type-specific limits
- MIME type must be valid for the declared file type

### Request Validation
- Authorization header required
- TYPE parameter must be "IMAGE"
- Content-Type must be multipart/form-data
- File field must contain valid file data

### S3 Path Generation
- UUID (7 digits) generated for unique folder structure
- File names are sanitized (special characters replaced with underscores)
- Path format: `questions/{uuid7digits}/{sanitized-filename}`

## Error Responses

All endpoints may return the following error responses:

### Validation Error (400)
```json
{
  "error": "ValidationError", 
  "message": "VALIDATION_ERROR: File size exceeds limit. Maximum allowed: 10MB",
  "formattedMessage": "File size exceeds limit. Maximum allowed: 10MB"
}
```

**Common validation errors:**
- Missing file in request
- Invalid file type
- File size exceeds limit
- Invalid MIME type
- Missing TYPE parameter
- Invalid multipart data

### Authorization Error (401)
```json
{
  "error": "AuthorizationError",
  "message": "AUTHENTICATION_ERROR: User authentication required", 
  "formattedMessage": "User authentication required"
}
```

### S3 Upload Error (500)
```json
{
  "error": "UnexpectedError",
  "message": "Failed to upload file to S3: Access denied",
  "formattedMessage": "An unexpected error occurred."
}
```

### Database Error (500)
```json
{
  "error": "UnexpectedError",
  "message": "Failed to save file record to database: Connection timeout",
  "formattedMessage": "An unexpected error occurred."
}
```

## Technical Details

### Multipart Form Data Parsing
- Supports standard multipart/form-data format
- Automatically extracts file metadata (name, size, MIME type)
- Handles base64 encoded request bodies from API Gateway

### S3 Integration
- Uses AWS SDK v3 for S3 operations
- Supports both S3 and S3-compatible services
- Automatic cleanup on upload failures
- Metadata stored with each file upload

### Database Tracking
- All uploads are tracked in MongoDB
- Includes user information, timestamps, and metadata
- Supports querying by user, file type, and upload date
- Maintains audit trail for all file operations

### Security Features
- User authentication required for all operations
- File type validation prevents malicious uploads
- File size limits prevent abuse
- Sanitized file paths prevent directory traversal
- Metadata includes source IP and user agent for auditing
