import { ValidationError } from '../../../utils/customErrors/index.js';

/**
 * Parse multipart form data from API Gateway event
 * @param {Object} event - API Gateway event object
 * @returns {Object} Parsed multipart data
 */
export function parseMultipartFromEvent(event) {
  if (!event.body) {
    throw new ValidationError('Request must contain multipart data');
  }

  // Handle both base64 encoded and raw body data
  let bodyBuffer;
  if (event.isBase64Encoded) {
    console.log('Parsing base64 encoded multipart data');
    bodyBuffer = Buffer.from(event.body, 'base64');
  } else {
    console.log('Parsing raw multipart data');
    // For Lambda Proxy Integration with binary data, body might be binary string
    // We need to handle this carefully to preserve binary data
    if (typeof event.body === 'string') {
      // Try to detect if it's already binary data
      bodyBuffer = Buffer.from(event.body, 'binary');
    } else {
      bodyBuffer = Buffer.from(event.body);
    }
  }

  const contentType = event.headers['content-type'] || event.headers['Content-Type'] || '';
  console.log('Content-Type:', contentType);
  
  if (!contentType.includes('multipart/form-data')) {
    throw new ValidationError('Request must be multipart/form-data');
  }

  // Extract boundary from content-type header
  const boundary = contentType.split('boundary=')[1];
  if (!boundary) {
    throw new ValidationError('Missing boundary in multipart data');
  }

  console.log('Boundary:', boundary);
  console.log('Body buffer length:', bodyBuffer.length);

  return parseMultipartData(bodyBuffer, boundary);
}

/**
 * Parse multipart form data to extract file and field information
 * @param {Buffer} bodyBuffer - Request body buffer
 * @param {string} boundary - Multipart boundary
 * @returns {Object} Parsed data with files and fields
 */
export function parseMultipartData(bodyBuffer, boundary) {
  try {
    const boundaryBuffer = Buffer.from(`--${boundary}`);
    const parts = [];
    let start = 0;

    // Find all parts separated by boundary
    while (true) {
      const boundaryIndex = bodyBuffer.indexOf(boundaryBuffer, start);
      if (boundaryIndex === -1) break;
      
      if (start > 0) {
        parts.push(bodyBuffer.slice(start, boundaryIndex));
      }
      start = boundaryIndex + boundaryBuffer.length;
    }

    const files = [];
    const fields = {};

    // Process each part
    for (const part of parts) {
      const headerEndIndex = part.indexOf('\r\n\r\n');
      if (headerEndIndex === -1) continue;

      const headerSection = part.slice(0, headerEndIndex).toString();
      const dataSection = part.slice(headerEndIndex + 4);

      // Parse Content-Disposition header
      const dispositionMatch = headerSection.match(/Content-Disposition:\s*([^\r\n]+)/i);
      if (!dispositionMatch) continue;

      const disposition = dispositionMatch[1];
      const nameMatch = disposition.match(/name="([^"]+)"/);
      if (!nameMatch) continue;

      const fieldName = nameMatch[1];

      // Check if this part contains a file
      if (headerSection.includes('filename=')) {
        // Extract filename
        const fileNameMatch = disposition.match(/filename="([^"]+)"/);
        const fileName = fileNameMatch ? fileNameMatch[1] : 'uploaded-file';

        // Extract content type with better parsing
        const contentTypeMatch = headerSection.match(/Content-Type:\s*([^\r\n;]+)/i);
        let mimeType = contentTypeMatch ? contentTypeMatch[1].trim() : null;
        
        // If no Content-Type found, try to infer from file extension
        if (!mimeType) {
          const ext = fileName.toLowerCase().split('.').pop();
          const mimeTypeMap = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'bmp': 'image/bmp',
            'svg': 'image/svg+xml'
          };
          mimeType = mimeTypeMap[ext] || 'application/octet-stream';
          console.log(`Inferred MIME type from extension .${ext}: ${mimeType}`);
        }
        
        console.log(`Parsed MIME type: ${mimeType} for file: ${fileName}`);

        // Remove trailing CRLF from data
        let fileBuffer = dataSection;
        if (fileBuffer.length >= 2 && 
            fileBuffer[fileBuffer.length - 2] === 0x0D && 
            fileBuffer[fileBuffer.length - 1] === 0x0A) {
          fileBuffer = fileBuffer.slice(0, -2);
        }

        files.push({
          fieldName,
          fileName,
          mimeType,
          fileBuffer,
          fileSize: fileBuffer.length
        });
      } else {
        // This is a regular form field
        let fieldValue = dataSection;
        if (fieldValue.length >= 2 && 
            fieldValue[fieldValue.length - 2] === 0x0D && 
            fieldValue[fieldValue.length - 1] === 0x0A) {
          fieldValue = fieldValue.slice(0, -2);
        }
        fields[fieldName] = fieldValue.toString();
      }
    }

    return { files, fields };
  } catch (error) {
    throw new ValidationError(`Failed to parse multipart data: ${error.message}`);
  }
}

/**
 * Extract the first file from multipart data
 * @param {Buffer} bodyBuffer - Request body buffer
 * @param {string} boundary - Multipart boundary
 * @returns {Object} First file found in the multipart data
 */
export function extractFirstFile(bodyBuffer, boundary) {
  const { files } = parseMultipartData(bodyBuffer, boundary);
  
  if (files.length === 0) {
    throw new ValidationError('No file found in multipart data');
  }

  const file = files[0];
  return {
    fileBuffer: file.fileBuffer,
    fileName: file.fileName,
    mimeType: file.mimeType,
    fileSize: file.fileSize
  };
}
