import S3Manager from '../utils/S3Manager.js';
import FileUploadRepository from '../repositories/fileUploadRepository.js';
import { FileUploadAggregate } from '../aggregate/fileUploadAggregate.js';
import { ValidationError, AuthorizationError } from '../../../utils/customErrors/index.js';

/**
 * Upload an image file to S3 and save the record to database
 * @param {Object} params - Upload parameters
 * @param {Buffer} params.fileBuffer - File buffer data
 * @param {string} params.fileName - Original file name
 * @param {string} params.mimeType - MIME type of the file
 * @param {number} params.fileSize - File size in bytes
 * @param {string} params.userId - ID of the user uploading the file
 * @param {string} params.type - File type (should be 'IMAGE')
 * @param {Object} params.metadata - Additional metadata
 * @returns {Promise<Object>} Upload result with file URL and metadata
 */
export const uploadImage = async ({
  fileBuffer,
  fileName,
  mimeType,
  fileSize,
  userId,
  type = 'IMAGE',
  metadata = {}
}) => {
  try {
    // Input validation
    if (!fileBuffer || !Buffer.isBuffer(fileBuffer)) {
      throw new ValidationError('Valid file buffer is required');
    }

    if (!fileName || typeof fileName !== 'string') {
      throw new ValidationError('File name is required');
    }

    if (!mimeType || typeof mimeType !== 'string') {
      throw new ValidationError('MIME type is required');
    }

    if (!fileSize || typeof fileSize !== 'number') {
      throw new ValidationError('File size is required');
    }

    if (!userId) {
      throw new ValidationError('User ID is required');
    }

    // Validate file type
    if (type !== 'IMAGE') {
      throw new ValidationError('Only IMAGE type is supported by this endpoint');
    }

    // Initialize S3Manager
    const s3Manager = new S3Manager();

    // Validate file type against MIME type
    console.log('Debug - MIME type received:', JSON.stringify(mimeType));
    console.log('Debug - File type:', type);
    
    // Clean and normalize MIME type
    const cleanMimeType = mimeType.toLowerCase().trim();
    console.log('Debug - Cleaned MIME type:', cleanMimeType);
    
    if (!s3Manager.validateFileType(cleanMimeType, type)) {
      throw new ValidationError(`Invalid file type. Expected image file, got: ${cleanMimeType}`);
    }

    // Check file size limits
    const maxFileSize = s3Manager.getMaxFileSize(type);
    if (fileSize > maxFileSize) {
      throw new ValidationError(`File size exceeds limit. Maximum allowed: ${Math.round(maxFileSize / (1024 * 1024))}MB`);
    }

    // Upload file to S3
    let uploadResult;
    try {
      uploadResult = await s3Manager.uploadFile(fileBuffer, fileName, cleanMimeType, type);
    } catch (error) {
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }

    // Prepare file upload data
    const fileUploadData = {
      fileName: uploadResult.key.split('/').pop(), // Extract file name from S3 key
      originalFileName: fileName,
      fileType: type,
      mimeType: cleanMimeType,
      fileSize,
      s3Key: uploadResult.key,
      s3Url: uploadResult.url,
      uploadedBy: userId,
      metadata: {
        ...metadata,
        uploadedAt: new Date().toISOString(),
        s3Bucket: process.env.S3_BUCKET_NAME
      }
    };

    // Create and validate file upload aggregate
    let fileUploadAggregate;
    try {
      fileUploadAggregate = FileUploadAggregate.fromData(fileUploadData);
    } catch (error) {
      // If validation fails, attempt to cleanup S3 file
      try {
        await s3Manager.deleteFile(uploadResult.key);
      } catch (cleanupError) {
        console.error('Failed to cleanup S3 file after validation error:', cleanupError);
      }
      throw error;
    }

    // Save file upload record to database
    let savedFileRecord;
    try {
      savedFileRecord = await FileUploadRepository.create({
        fileUploadData: fileUploadAggregate.toData()
      });
    } catch (error) {
      // If database save fails, attempt to cleanup S3 file
      try {
        await s3Manager.deleteFile(uploadResult.key);
      } catch (cleanupError) {
        console.error('Failed to cleanup S3 file after database error:', cleanupError);
      }
      throw new Error(`Failed to save file record to database: ${error.message}`);
    }

    return {
      success: true,
      message: 'Image uploaded successfully',
      data: {
        id: savedFileRecord._id,
        fileName: savedFileRecord.fileName,
        originalFileName: savedFileRecord.originalFileName,
        fileType: savedFileRecord.fileType,
        mimeType: savedFileRecord.mimeType,
        fileSize: savedFileRecord.fileSize,
        url: savedFileRecord.s3Url,
        s3Key: savedFileRecord.s3Key,
        uploadedAt: savedFileRecord.uploadedAt,
        metadata: savedFileRecord.metadata
      }
    };
  } catch (error) {
    // Re-throw specific errors
    if (error instanceof ValidationError || error instanceof AuthorizationError) {
      throw error;
    }
    
    // Log unexpected errors
    console.error('Unexpected error in uploadImage:', error);
    throw new Error(`Failed to upload image: ${error.message}`);
  }
};
