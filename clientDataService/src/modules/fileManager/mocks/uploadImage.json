{"success": true, "message": "Image uploaded successfully", "data": {"id": "64f8a3b2c1d4e5f6a7b8c9d0", "fileName": "sample_image.jpg", "originalFileName": "sample_image.jpg", "fileType": "IMAGE", "mimeType": "image/jpeg", "fileSize": 102400, "url": "s3://qaas-service-prod-qaas-s3/questions/a1b2c3d/sample_image.jpg", "s3Key": "questions/a1b2c3d/sample_image.jpg", "uploadedAt": "2023-12-01T10:30:00.000Z", "metadata": {"userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "sourceIp": "*************", "uploadMethod": "multipart", "uploadedAt": "2023-12-01T10:30:00.000Z", "s3Bucket": "qaas-service-prod-qaas-s3"}}}