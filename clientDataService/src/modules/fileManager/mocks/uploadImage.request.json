{"description": "Mock request for image upload (multipart form data)", "httpMethod": "POST", "path": "/fileManager/images/upload", "queryStringParameters": {"TYPE": "IMAGE"}, "headers": {"Content-Type": "multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "requestContext": {"authorizer": {"id": "user123", "role": "student"}, "identity": {"sourceIp": "*************"}}, "isBase64Encoded": true, "body": "LS0tLS0tV2ViS2l0Rm9ybUJvdW5kYXJ5N01BNFlXeGtUclp1MGdXDQpDb250ZW50LURpc3Bvc2l0aW9uOiBmb3JtLWRhdGE7IG5hbWU9ImZpbGUiOyBmaWxlbmFtZT0ic2FtcGxlX2ltYWdlLmpwZyINCkNvbnRlbnQtVHlwZTogaW1hZ2UvanBlZw0KDQovOWovNEFBUVNrWkpSZ0FCQVFFQVlBQmdBQUQvNFFhSWFYWnZZWGswWXpNeE1BQUJBUUVBWVhaaGNnQUJBUUVBWVhaaGNnQUFBZC8vDQotLS0tLS1XZWJLaXRGb3JtQm91bmRhcnk3TUE0WVd4a1RyWnUwZ1ctLQ0K", "note": "The body contains base64 encoded multipart form data with the image file. In a real request, this would be the actual binary data of the uploaded image file."}