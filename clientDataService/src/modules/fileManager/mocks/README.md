# File Manager Module Mocks

This directory contains mock data for testing and development of the File Manager module.

## Mock Files

### Request/Response Mocks

- **`uploadImage.json`** - Response mock for successful image upload
- **`uploadImage.request.json`** - Request mock for image upload (multipart form data)

## File Upload Types

- **IMAGE** - Image files (JPEG, PNG, GIF, WebP, BMP, SVG)
- **DOCUMENT** - Document files (PDF, Word, Excel, etc.) - Future support
- **VIDEO** - Video files (MP4, MOV, AVI, etc.) - Future support

## Properties

### Upload Response Properties
- `success` - <PERSON><PERSON>an indicating successful upload
- `message` - Human-readable success message
- `data` - Object containing file metadata
  - `id` - Database record ID
  - `fileName` - Final file name in S3
  - `originalFileName` - Original uploaded file name
  - `fileType` - File type (IMAGE, DOCUMENT, VIDEO)
  - `mimeType` - MIME type of the file
  - `fileSize` - File size in bytes
  - `url` - S3 URL for accessing the file
  - `s3Key` - S3 object key
  - `uploadedAt` - Upload timestamp
  - `metadata` - Additional metadata object

### Upload Request Properties
- `queryStringParameters.TYPE` - File type parameter (required)
- `headers.Content-Type` - Must be multipart/form-data
- `headers.Authorization` - Bearer token for authentication
- `requestContext.authorizer` - User authentication context
- `isBase64Encoded` - Must be true for multipart data
- `body` - Base64 encoded multipart form data with file

## S3 Storage Structure

Files are stored with the following path structure:
```
s3://qaas-service-prod-qaas-s3/
  └── questions/
      └── {uuid7digits}/
          └── {sanitized-filename}
```

## Usage

These mocks can be used for:
- Unit testing file upload functionality
- Integration testing with S3 and database
- API documentation examples
- Frontend development and testing
- Load testing and performance validation

## File Size Limits

- **Images**: 10MB maximum
- **Documents**: 50MB maximum (future)
- **Videos**: 500MB maximum (future)

## Supported MIME Types

### Images
- `image/jpeg`, `image/jpg`
- `image/png`
- `image/gif`
- `image/webp`
- `image/bmp`
- `image/svg+xml`

## Error Scenarios

Common error scenarios to test:
- File size exceeds limit
- Invalid file type
- Missing TYPE parameter
- Invalid authentication
- S3 upload failures
- Database save failures
- Malformed multipart data
