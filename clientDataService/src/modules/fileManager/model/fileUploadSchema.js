import mongoose from 'mongoose';

const fileUploadSchema = new mongoose.Schema({
  fileName: {
    type: String,
    required: true
  },
  originalFileName: {
    type: String,
    required: true
  },
  fileType: {
    type: String,
    required: true,
    enum: ['IMAGE', 'DOCUMENT', 'VIDEO']
  },
  mimeType: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  s3Key: {
    type: String,
    required: true,
    unique: true
  },
  s3Url: {
    type: String,
    required: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, { 
  timestamps: true,
  collection: 'file_uploads'
});

// Index for faster queries
fileUploadSchema.index({ uploadedBy: 1, uploadedAt: -1 });
fileUploadSchema.index({ fileType: 1 });
fileUploadSchema.index({ s3Key: 1 });

export default mongoose.model('FileUpload', fileUploadSchema);
