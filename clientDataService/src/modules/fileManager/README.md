# File Manager Module

This module provides file upload functionality to S3 storage with database tracking.

## Features

- Image upload to S3 with automatic path generation (questions/{{uuid7digits}}/file-name)
- File validation (type, size, MIME type)
- Database record tracking
- Multipart form data parsing
- Error handling with S3 cleanup on failures

## Endpoints

### POST /fileManager/images/upload

Upload an image file to S3.

**Parameters:**
- Query parameter: `TYPE=IMAGE` (required)
- Body: multipart/form-data with file

**Example:**
```bash
curl -X POST "https://api.example.com/fileManager/images/upload?TYPE=IMAGE" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg"
```

**Response:**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "data": {
    "id": "64f...",
    "fileName": "image.jpg",
    "originalFileName": "image.jpg",
    "fileType": "IMAGE",
    "mimeType": "image/jpeg",
    "fileSize": 102400,
    "url": "s3://qaas-service-prod-qaas-s3/questions/1234567/image.jpg",
    "s3Key": "questions/1234567/image.jpg",
    "uploadedAt": "2023-12-01T10:30:00.000Z",
    "metadata": {...}
  }
}
```

## S3 Path Structure

Files are stored in S3 with the following path structure:
```
questions/
  └── {uuid7digits}/
      └── {sanitized-filename}
```

Example: `questions/a1b2c3d/my_image.jpg`

**S3 Bucket:** `qaas-service-prod-qaas-s3`  
**Full S3 URL:** `s3://qaas-service-prod-qaas-s3/questions/a1b2c3d/my_image.jpg`

## File Type Support

### Images (TYPE=IMAGE)
- Supported formats: JPEG, PNG, GIF, WebP, BMP, SVG
- Max size: 10MB

### Future Support
- Documents (TYPE=DOCUMENT): PDF, Word, Excel, etc. - 50MB limit
- Videos (TYPE=VIDEO): MP4, MOV, AVI, etc. - 500MB limit

## Error Handling

The module includes comprehensive error handling:
- Input validation
- File type and size validation
- S3 upload error handling
- Database error handling with S3 cleanup
- Multipart parsing error handling

## Architecture

The module follows the clean architecture pattern:

- **Handler**: Request/response handling and routing
- **Use Cases**: Business logic implementation
- **Repository**: Data access layer
- **Aggregate**: Domain logic and validation
- **Utils**: S3Manager for file operations
- **Libs**: Shared utilities (multipart parser)
- **Model**: Database schema definition
