import { CompositeValidator, Validator } from '../../../utils/validators/index.js';
import { TypeValidator, EnumValidator } from '../../../utils/validators/classes/basic.js';
import { ValidationError } from '../../../utils/customErrors/index.js';

// Define validators at the module level
const fileUploadTypesValidator = new TypeValidator({
  fileName: 'string',
  originalFileName: 'string',
  fileType: 'string',
  mimeType: 'string',
  fileSize: 'number',
  s3Key: 'string',
  s3Url: 'string',
  uploadedBy: 'string',
  metadata: 'object'
});

const fileTypeEnumValidator = new EnumValidator({
  allowedValues: {
    fileType: ['IMAGE', 'DOCUMENT', 'VIDEO']
  }
});

// Custom validator for file size
class FileSizeValidator extends Validator {
  validate(data) {
    if (typeof data.fileSize !== 'number' || data.fileSize <= 0) {
      throw new ValidationError('File size must be a positive number');
    }
    return true;
  }
}

// Custom validator for S3 key format
class S3KeyValidator extends Validator {
  validate(data) {
    if (!data.s3Key.startsWith('questions/')) {
      throw new ValidationError('S3 key must start with questions/');
    }
    return true;
  }
}

// Custom validator for S3 URL format
class S3UrlValidator extends Validator {
  validate(data) {
    if (!data.s3Url.startsWith('s3://') && !data.s3Url.startsWith('https://')) {
      throw new ValidationError('S3 URL must start with s3:// or https://');
    }
    return true;
  }
}

// Custom validator for MIME type format
class MimeTypeValidator extends Validator {
  validate(data) {
    if (!data.mimeType.includes('/')) {
      throw new ValidationError('Invalid MIME type format');
    }
    return true;
  }
}

// Create the composite validator
const fileUploadValidator = new CompositeValidator(fileUploadTypesValidator);
fileUploadValidator.setNext(fileTypeEnumValidator);
fileUploadValidator.setNext(new FileSizeValidator());
fileUploadValidator.setNext(new S3KeyValidator());
fileUploadValidator.setNext(new S3UrlValidator());
fileUploadValidator.setNext(new MimeTypeValidator());

export class FileUploadAggregate {
  constructor(data) {
    this.fileName = data.fileName;
    this.originalFileName = data.originalFileName;
    this.fileType = data.fileType;
    this.mimeType = data.mimeType;
    this.fileSize = data.fileSize;
    this.s3Key = data.s3Key;
    this.s3Url = data.s3Url;
    this.uploadedBy = data.uploadedBy;
    this.uploadedAt = data.uploadedAt;
    this.metadata = data.metadata || {};
  }

  /**
   * Create FileUploadAggregate from raw data with validation
   * @param {Object} data - Raw file upload data
   * @returns {FileUploadAggregate}
   */
  static fromData(data) {
    // Run validation
    fileUploadValidator.validate(data);

    return new FileUploadAggregate({
      ...data,
      uploadedAt: data.uploadedAt || new Date(),
      metadata: data.metadata || {}
    });
  }

  /**
   * Convert aggregate to plain data object
   * @returns {Object}
   */
  toData() {
    return {
      fileName: this.fileName,
      originalFileName: this.originalFileName,
      fileType: this.fileType,
      mimeType: this.mimeType,
      fileSize: this.fileSize,
      s3Key: this.s3Key,
      s3Url: this.s3Url,
      uploadedBy: this.uploadedBy,
      uploadedAt: this.uploadedAt,
      metadata: this.metadata
    };
  }

  /**
   * Convert aggregate to DTO for external use
   * @returns {Object}
   */
  toDTO() {
    return {
      fileName: this.fileName,
      originalFileName: this.originalFileName,
      fileType: this.fileType,
      mimeType: this.mimeType,
      fileSize: this.fileSize,
      url: this.s3Url,
      uploadedAt: this.uploadedAt,
      metadata: {
        ...this.metadata,
        // Remove sensitive metadata for external use
        s3Bucket: undefined,
        s3Key: undefined
      }
    };
  }

  /**
   * Check if file is an image
   * @returns {boolean}
   */
  isImage() {
    return this.fileType === 'IMAGE' && this.mimeType.startsWith('image/');
  }

  /**
   * Check if file is a document
   * @returns {boolean}
   */
  isDocument() {
    return this.fileType === 'DOCUMENT';
  }

  /**
   * Check if file is a video
   * @returns {boolean}
   */
  isVideo() {
    return this.fileType === 'VIDEO' && this.mimeType.startsWith('video/');
  }

  /**
   * Get file size in human readable format
   * @returns {string}
   */
  getHumanReadableSize() {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
  }

  /**
   * Get file extension from original filename
   * @returns {string}
   */
  getFileExtension() {
    const parts = this.originalFileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * Validate if file belongs to user
   * @param {string} userId - User ID to check
   * @returns {boolean}
   */
  belongsToUser(userId) {
    return this.uploadedBy.toString() === userId.toString();
  }
}
