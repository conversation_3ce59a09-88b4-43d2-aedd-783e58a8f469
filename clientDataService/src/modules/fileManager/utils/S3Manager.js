import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

class S3Manager {
  constructor() {
    this.s3Client = new S3Client({
      region: process.env.REGION,
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
      },
      endpoint: process.env.S3_ENDPOINT,
      forcePathStyle: true, // Required for some S3-compatible services
    });
    
    this.bucketName = process.env.S3_BUCKET_NAME;
    this.baseStorage = 'questions';
  }

  /**
   * Generate a unique file key for S3 storage
   * @param {string} type - File type (IMAGE, DOCUMENT, VIDEO)
   * @param {string} originalFileName - Original file name
   * @returns {string} S3 key
   */
  generateFileKey(type, originalFileName) {
    const uuid7digits = uuidv4().substring(0, 7);
    const fileExtension = path.extname(originalFileName);
    const sanitizedFileName = originalFileName
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/__+/g, '_');
    
    // Use questions folder structure: questions/{uuid7digits}/file-name
    return `${this.baseStorage}/${uuid7digits}/${sanitizedFileName}`;
  }

  /**
   * Upload a file to S3
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} fileName - File name
   * @param {string} mimeType - MIME type
   * @param {string} type - File type (IMAGE, DOCUMENT, VIDEO)
   * @returns {Promise<{key: string, url: string}>}
   */
  async uploadFile(fileBuffer, fileName, mimeType, type = 'IMAGE') {
    try {
      const key = this.generateFileKey(type, fileName);
      
      const uploadParams = {
        Bucket: this.bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: mimeType,
        // Add metadata
        Metadata: {
          'original-name': fileName,
          'upload-type': type,
          'upload-date': new Date().toISOString(),
        },
      };

      const command = new PutObjectCommand(uploadParams);
      await this.s3Client.send(command);

      // Generate the public URL using the proper HTTPS format
      const url = `https://${this.bucketName}.s3.${process.env.REGION}.amazonaws.com/${key}`;

      return {
        key,
        url,
      };
    } catch (error) {
      throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
  }

  /**
   * Delete a file from S3
   * @param {string} key - S3 key
   * @returns {Promise<boolean>}
   */
  async deleteFile(key) {
    try {
      const deleteParams = {
        Bucket: this.bucketName,
        Key: key,
      };

      const command = new DeleteObjectCommand(deleteParams);
      await this.s3Client.send(command);
      
      return true;
    } catch (error) {
      throw new Error(`Failed to delete file from S3: ${error.message}`);
    }
  }

  /**
   * Generate a presigned URL for file access
   * @param {string} key - S3 key
   * @param {number} expiresIn - Expiration time in seconds (default: 1 hour)
   * @returns {Promise<string>} Presigned URL
   */
  async getPresignedUrl(key, expiresIn = 3600) {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      return url;
    } catch (error) {
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Check if a file exists in S3
   * @param {string} key - S3 key
   * @returns {Promise<boolean>}
   */
  async fileExists(key) {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NoSuchKey' || error.$metadata?.httpStatusCode === 404) {
        return false;
      }
      throw new Error(`Failed to check file existence: ${error.message}`);
    }
  }

  /**
   * Validate file type for upload
   * @param {string} mimeType - MIME type
   * @param {string} type - Expected file type
   * @returns {boolean}
   */
  validateFileType(mimeType, type) {
    const allowedTypes = {
      IMAGE: [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml'
      ],
      DOCUMENT: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ],
      VIDEO: [
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo',
        'video/webm'
      ]
    };

    return allowedTypes[type]?.includes(mimeType) || false;
  }

  /**
   * Get file size limits by type (in bytes)
   * @param {string} type - File type
   * @returns {number} Max file size in bytes
   */
  getMaxFileSize(type) {
    const limits = {
      IMAGE: 10 * 1024 * 1024, // 10MB
      DOCUMENT: 50 * 1024 * 1024, // 50MB
      VIDEO: 500 * 1024 * 1024, // 500MB
    };

    return limits[type] || 10 * 1024 * 1024; // Default 10MB
  }
}

export default S3Manager;
