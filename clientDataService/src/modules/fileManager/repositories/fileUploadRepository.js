import FileUpload from '../model/fileUploadSchema.js';
import { NotFoundError } from '../../../utils/customErrors/index.js';

class FileUploadRepository {
  static async create({fileUploadData, session}) {
    try {
      const options = session ? { session } : {};
      const fileUpload = new FileUpload(fileUploadData);
      const result = await fileUpload.save(options);
      return result.toObject();
    } catch (error) {
      throw new Error(`Error creating file upload record: ${error.message}`);
    }
  }

  static async findById({id, userId}) {
    try {
      const query = { _id: id };
      if (userId) {
        query.uploadedBy = userId;
      }
      
      const fileUpload = await FileUpload.findOne(query);
      if (!fileUpload) {
        throw new NotFoundError('File upload record not found');
      }
      return fileUpload.toObject();
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error finding file upload: ${error.message}`);
    }
  }

  static async findByS3Key({s3Key}) {
    try {
      const fileUpload = await FileUpload.findOne({ s3Key });
      if (!fileUpload) {
        throw new NotFoundError('File upload record not found');
      }
      return fileUpload.toObject();
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error finding file upload by S3 key: ${error.message}`);
    }
  }

  static async findAll({filter = {}, userId, limit = 50, page = 1}) {
    try {
      const query = { ...filter };
      if (userId) {
        query.uploadedBy = userId;
      }

      const skip = (page - 1) * limit;
      
      const [files, total] = await Promise.all([
        FileUpload.find(query)
          .sort({ uploadedAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        FileUpload.countDocuments(query)
      ]);

      return {
        files,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new Error(`Error fetching file uploads: ${error.message}`);
    }
  }

  static async delete({id, userId}) {
    try {
      const query = { _id: id };
      if (userId) {
        query.uploadedBy = userId;
      }
      
      const result = await FileUpload.findOneAndDelete(query);
      if (!result) {
        throw new NotFoundError('File upload record not found');
      }
      return result.toObject();
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error deleting file upload: ${error.message}`);
    }
  }

  static async update({id, data, userId}) {
    try {
      const query = { _id: id };
      if (userId) {
        query.uploadedBy = userId;
      }
      
      const result = await FileUpload.findOneAndUpdate(
        query,
        { $set: data },
        { new: true, runValidators: true }
      );
      
      if (!result) {
        throw new NotFoundError('File upload record not found');
      }
      return result.toObject();
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error(`Error updating file upload: ${error.message}`);
    }
  }
}

export default FileUploadRepository;
