import { uploadImage } from '../../useCases/uploadImage.js';
import { ValidationError, AuthorizationError } from "../../../../utils/customErrors/index.js";
import { parseMultipartFromEvent } from '../../libs/multipartParser.js';

export const handler = async (event) => {
  try {
    if (event.source === "serverless-plugin-warmup") {
      return "Lambda warmed up!";
    }

    // Debug log the incoming event
    console.log('Event debug:', {
      httpMethod: event.httpMethod,
      resource: event.resource,
      isBase64Encoded: event.isBase64Encoded,
      bodyType: typeof event.body,
      bodyLength: event.body ? event.body.length : 0,
      headers: event.headers,
      queryStringParameters: event.queryStringParameters
    });

    // Get user ID from authorizer
    const { id: userId } = event.requestContext.authorizer || {};
    if (!userId) {
      throw new AuthorizationError('User authentication required');
    }

    console.log("userId", userId);
    
    // Get type from query string parameters or body
    const type = event.queryStringParameters?.TYPE || 
                 (event.body && JSON.parse(event.body)?.TYPE) || 
                 'IMAGE';
    
    // Validate type parameter
    if (type !== 'IMAGE') {
      throw new ValidationError('Only IMAGE type is supported. Use TYPE=IMAGE in query parameters or request body');
    }

    // Parse multipart form data
    const { files, fields } = parseMultipartFromEvent(event);
    
    if (files.length === 0) {
      throw new ValidationError('No file found in request');
    }

    // Get the first file (assuming single file upload)
    const file = files[0];
    const { fileBuffer, fileName, mimeType, fileSize } = file;
    
    console.log('Handler Debug - File details:', {
      fileName,
      mimeType,
      fileSize,
      type
    });

    // Call the use case
    const result = await uploadImage({
      fileBuffer,
      fileName,
      mimeType,
      fileSize,
      userId,
      type,
      metadata: {
        userAgent: event.headers['user-agent'] || '',
        sourceIp: event.requestContext.identity?.sourceIp || '',
        uploadMethod: 'multipart'
      }
    });

    return result;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
};


