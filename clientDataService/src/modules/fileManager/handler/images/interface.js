import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import connectToDB from '../../../../infra/libs/mongodb/connect.js';
import disconnectFromDB from '../../../../infra/libs/mongodb/disconnect.js';

// Import file manager handlers
import { handler as uploadImageHandler } from "./uploadImage.js";

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("File Manager Duration");
    const functions = {
      // Image upload endpoints
      "/fileManager/images/upload": {
        POST: uploadImageHandler
      }
    };

    const resource = event.resource;
    const method = event.method || event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("File Manager Duration");
    disconnectFromDB();
    return apiResponse(200, {body: response});
  } catch (error) {
    disconnectFromDB();
    console.log("FileManager", error.message);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
