import { apiResponse } from "../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../utils/customErrors/index.js";
import connectToDB from '../../../infra/libs/mongodb/connect.js';
import disconnectFromDB from '../../../infra/libs/mongodb/disconnect.js';

// Import individual handlers
import { handler as createGroupHandler } from "./createGroup.js";
import { handler as updateGroupHandler } from "./updateGroup.js";
import { handler as updateGroupMembersHandler } from "./updateGroupMembers.js";
import { handler as getGroupByIdHandler } from "./getGroupById.js";
import { handler as getAllGroupsHandler } from "./getAllGroups.js";
import { handler as deleteGroupHandler } from "./deleteGroup.js";
import { handler as getGroupMembersHandler } from "./getGroupMembers.js";
import { handler as fetchUserGroupsHandler } from "./fetchUserGroups.js";
import { handler as fetchNestedGroupsHandler } from "./fetchNestedGroups.js";
import { handler as getGroupsStudentsAverageHandler } from "./getGroupsStudentsAverage.js";

export async function handler(event) {
  await connectToDB();
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Groups Management Duration");
    const functions = {
      // Basic group operations
      "/group": {
        GET: getAllGroupsHandler,
        POST: createGroupHandler
      },
      "/group/byId/{groupId}": {
        GET: getGroupByIdHandler,
        PATCH: updateGroupHandler
      },
      "/group/byId": {
        DELETE: deleteGroupHandler
      },
      "/group/members/{groupId}": {
        PATCH: updateGroupMembersHandler
      },
      "/group/members": {
        GET: getGroupMembersHandler
      },
      "/group/user/groups/{userId}": {
        GET: fetchUserGroupsHandler
      },
      "/group/nested": {
        GET: fetchNestedGroupsHandler
      },
      "/group/students/average": {
        GET: getGroupsStudentsAverageHandler
      }
    };

    const resource = event.resource;
    const method = event.method || event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log("[GROUPS INTERFACE] RESPONSE", {response});
    console.timeEnd("Groups Management Duration");
    disconnectFromDB();
    return apiResponse(201, {body: response});
  } catch (error) {
    disconnectFromDB();
    console.log("GroupsManagement error", error.message);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
