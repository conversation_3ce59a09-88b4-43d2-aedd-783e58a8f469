import { LessonMetricModel } from '../../../domain/models/metrics/lessonMetricsModel.js';
import { GroupModel } from '../../groups/schema/groupSchema.js';
import { ValidationError, NotFoundError, UnauthorizedError } from '../../../utils/customErrors/index.js';

/**
 * Get comments by group ID (for teachers to see all comments from their students)
 * @param {Object} params - Parameters
 * @param {string} params.groupId - Group identifier
 * @param {string} params.videoId - Optional video identifier to filter comments
 * @param {boolean} params.isDoubt - Whether to filter only doubts
 * @param {number} params.page - Page number for pagination
 * @param {number} params.limit - Items per page
 * @param {string} params.userId - The user's ID
 * @returns {Promise<Object>} Comments with pagination info
 */
export async function getCommentsByGroupId({ 
    groupId, 
    videoId, 
    isDoubt = false, 
    page = 1, 
    limit = 20, 
    userId
}) {
    // Validate input parameters
    if (!groupId) {
        throw new ValidationError('groupId is required');
    }

    // Verify group exists and get its members
    const group = await GroupModel.findById(groupId);
    if (!group) {
        throw new NotFoundError('Group not found');
    }

    if (!group.members.includes(userId)) {
        throw new UnauthorizedError('The user is not a member of this group');
    }

    const memberIds = group.members;
    if (!memberIds || memberIds.length === 0) {
        return {
            comments: [],
            pagination: {
                page,
                limit,
                total: 0,
                totalPages: 0,
                hasNext: false,
                hasPrevious: false
            },
            groupInfo: {
                groupId: group._id,
                groupName: group.name,
                category: group.category,
                membersCount: 0
            }
        };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Match criteria
    const matchCriteria = {
        userId: { $in: memberIds }
    };

    // Add video filter if provided
    if (videoId) {
        matchCriteria.videoId = parseInt(videoId);
    }

    // Add filter for comments or doubts
    if (isDoubt) {
        // Filter for doubts with status -1 (pending doubts)
        matchCriteria.doubts = { 
            $elemMatch: { status: -1 } 
        };
    } else {
        // Filter for regular comments (non-doubts)
        matchCriteria.comments = { $exists: true, $not: { $size: 0 } };
    }

    const pipeline = [
        { $match: matchCriteria },
        {
            $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'user',
                pipeline: [
                    {
                        $project: {
                            name: 1,
                            email: 1,
                            role: 1
                        }
                    }
                ]
            }
        },
        { $unwind: '$user' },
        {
            $project: {
                userId: 1,
                videoId: 1,
                playlistId: 1,
                subject: 1,
                comments: 1,
                doubts: 1,
                createdAt: 1,
                updatedAt: 1,
                user: 1
            }
        },
        { $sort: { updatedAt: -1 } },
        { $skip: skip },
        { $limit: limit }
    ];

    // Execute aggregation
    const [results, totalCount] = await Promise.all([
        LessonMetricModel.aggregate(pipeline),
        LessonMetricModel.countDocuments(matchCriteria)
    ]);

    const processedComments = [];
    
    results.forEach(lessonMetric => {
        const { user, videoId, playlistId, subject, userId, createdAt, updatedAt } = lessonMetric;
        
        if (isDoubt && lessonMetric.doubts) {
            // Filter doubts with status -1 (pending doubts)
            lessonMetric.doubts
                .filter(doubt => doubt.status === -1)
                .forEach(doubt => {
                    processedComments.push({
                        commentId: doubt.commentId,
                        userId,
                        videoId,
                        playlistId,
                        subject,
                        user,
                        isDoubt: true,
                        doubtStatus: doubt.status,
                        createdAt,
                        updatedAt
                    });
                });
        } else if (!isDoubt && lessonMetric.comments) {
            lessonMetric.comments.forEach(commentId => {
                processedComments.push({
                    commentId,
                    userId,
                    videoId,
                    playlistId,
                    subject,
                    user,
                    isDoubt: false,
                    createdAt,
                    updatedAt
                });
            });
        }
    });

    // Calculate pagination
    const totalPages = Math.ceil(totalCount / limit);

    return {
        comments: processedComments,
        pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext: page < totalPages,
            hasPrevious: page > 1
        },
        groupInfo: {
            groupId: group._id,
            groupName: group.name,
            category: group.category,
            membersCount: memberIds.length
        }
    };
}