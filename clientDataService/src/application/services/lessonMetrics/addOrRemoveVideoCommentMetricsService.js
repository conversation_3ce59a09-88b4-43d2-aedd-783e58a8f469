import UserRepository from "../../../domain/repositories/userRepository.js";
import { NotFoundError } from "../../../utils/customErrors/index.js";

const userRepository = new UserRepository();

/**
 * Service to add or remove video comment metrics for a user
 * @param {Object} params - Service parameters
 * @param {string} params.userId - User identifier
 * @param {string} params.videoId - Video identifier
 * @param {string} params.commentId - Comment identifier
 * @param {boolean} params.isDoubt - Whether the comment is a doubt
 * @param {boolean} params.isRemove - Whether to remove the comment
 * @returns {Promise<Object>} Updated metrics or success message
 */
export default async function addOrRemoveVideoCommentMetricsService({ 
    userId, 
    videoId, 
    commentId, 
    isDoubt = false, 
    isRemove = false 
}) {
    const userAggregate = await findUserWithMetrics(userId);
    const existingLessonMetric = userAggregate.getSingleLessonMetric({ videoId, userId });

    if (!existingLessonMetric) {
        return await handleNonExistentLessonMetric({ 
            userId, 
            videoId, 
            commentId, 
            isDoubt, 
            isRemove 
        });
    }

    return await updateExistingLessonMetric(existingLessonMetric, { commentId, isDoubt, isRemove });
}

/**
 * Find user with lesson metrics
 * @param {string} userId - User identifier
 * @returns {Promise<Object>} User aggregate with metrics
 * @throws {NotFoundError} When user is not found
 */
async function findUserWithMetrics(userId) {
    const userAggregate = await userRepository.findByIdWithMetrics({ 
        userId, 
        metricsArray: ['lessonMetrics'] 
    });

    if (!userAggregate) {
        throw new NotFoundError('User not found');
    }

    return userAggregate;
}

/**
 * Handle case when lesson metric doesn't exist
 * @param {Object} params - Parameters for creating new lesson metric
 * @returns {Promise<Object>} Result of creation or skip message
 */
async function handleNonExistentLessonMetric({ userId, videoId, commentId, isDoubt, isRemove }) {
    if (isRemove) {
        return { 
            message: "Lesson metric doesn't exist, skipping removal", 
            comments: [], 
            doubts: [] 
        };
    }

    const newLessonMetric = createNewLessonMetric({ userId, videoId, commentId, isDoubt });
    await userRepository.addLessonMetricValue(newLessonMetric);
    
    return { 
        message: "New lesson metric created successfully", 
        comments: newLessonMetric.comments, 
        doubts: newLessonMetric.doubts 
    };
}

/**
 * Create a new lesson metric object
 * @param {Object} params - Parameters for new lesson metric
 * @returns {Object} New lesson metric object
 */
function createNewLessonMetric({ userId, videoId, commentId, isDoubt }) {
    return {
        userId,
        videoId,
        playlistId: null, // TODO: Add playlistId parameter if needed
        subject: "",
        olympiadId: "",
        type: "MODULE",
        videoOwnerId: "atomize",
        watched: false,
        tags: [],
        watchedAt: null,
        isLiked: false,
        comments: [commentId],
        doubts: isDoubt ? [{ commentId, status: -1 }] : []
    };
}

/**
 * Update existing lesson metric with comment operations
 * @param {Object} lessonMetric - Existing lesson metric
 * @param {Object} params - Update parameters
 * @returns {Promise<Object>} Updated lesson metric
 */
async function updateExistingLessonMetric(lessonMetric, { commentId, isDoubt, isRemove }) {
    const updatedMetric = { ...lessonMetric };
    
    // Ensure arrays exist
    ensureArrayProperties(updatedMetric);
    
    if (isRemove) {
        removeCommentFromMetric(updatedMetric, commentId);
    } else {
        addCommentToMetric(updatedMetric, commentId, isDoubt);
    }

    return await userRepository.updateLessonMetric(updatedMetric);
}

/**
 * Ensure comments and doubts properties are arrays
 * @param {Object} metric - Lesson metric object
 */
function ensureArrayProperties(metric) {
    if (!Array.isArray(metric.comments)) {
        metric.comments = [];
    }
    
    if (!Array.isArray(metric.doubts)) {
        metric.doubts = [];
    }
}

/**
 * Add comment to lesson metric
 * @param {Object} metric - Lesson metric object
 * @param {string} commentId - Comment identifier
 * @param {boolean} isDoubt - Whether comment is a doubt
 */
function addCommentToMetric(metric, commentId, isDoubt) {
    // Add comment if not already present
    if (!metric.comments.includes(commentId)) {
        metric.comments.push(commentId);
    }

    // Add doubt if it's a doubt and not already present
    if (isDoubt && !metric.doubts.find(doubt => doubt.commentId === commentId)) {
        metric.doubts.push({ commentId, status: -1 });
    }
}

/**
 * Remove comment from lesson metric
 * @param {Object} metric - Lesson metric object
 * @param {string} commentId - Comment identifier to remove
 */
function removeCommentFromMetric(metric, commentId) {
    metric.comments = metric.comments.filter(id => id !== commentId);
    metric.doubts = metric.doubts.filter(doubt => doubt.commentId !== commentId);
}