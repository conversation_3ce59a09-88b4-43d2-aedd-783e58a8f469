{"Stacks": [{"StackId": "arn:aws:cloudformation:us-east-2:010526255193:stack/cds-service-dev/f3aac750-6c04-11f0-b608-06361b4ff3ad", "StackName": "cds-service-dev", "Description": "The AWS CloudFormation template for this Serverless application", "CreationTime": "2025-07-28T22:48:17.490000+00:00", "DeletionTime": "2025-09-02T00:21:07.121000+00:00", "LastUpdatedTime": "2025-09-01T21:48:59.072000+00:00", "RollbackConfiguration": {}, "StackStatus": "DELETE_IN_PROGRESS", "DisableRollback": false, "NotificationARNs": [], "Capabilities": ["CAPABILITY_IAM", "CAPABILITY_NAMED_IAM"], "Outputs": [{"OutputKey": "CommunicationInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-communicationInterface:12", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CommunicationInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FindLessonMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindLessonMetrics:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindLessonMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateSchoolLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-UpdateSchool:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateSchoolLambdaFunctionQualifiedArn"}, {"OutputKey": "BatchAnswerFlashcardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-batchAnswerFlashcards:59", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-BatchAnswerFlashcardsLambdaFunctionQualifiedArn"}, {"OutputKey": "FindQuestionMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindQuestionMetrics:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindQuestionMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "CalendarEventsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-calendarEventsInterface:27", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CalendarEventsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "CompareWithDistrictLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-compareWithDistrict:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CompareWithDistrictLambdaFunctionQualifiedArn"}, {"OutputKey": "ListSelfDecksLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-listSelfDecks:54", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ListSelfDecksLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoSaveLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoSave:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoSaveLambdaFunctionQualifiedArn"}, {"OutputKey": "ServerlessDeploymentBucketName", "OutputValue": "cds-service-dev-deployment-bucket", "ExportName": "sls-cds-service-dev-ServerlessDeploymentBucketName"}, {"OutputKey": "CreateSchoolLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-CreateSchool:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateSchoolLambdaFunctionQualifiedArn"}, {"OutputKey": "ClusterByOlympiadLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-ClusterByOlympiad:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ClusterByOlympiadLambdaFunctionQualifiedArn"}, {"OutputKey": "GetGeneralStatisticsForUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetGeneralStatisticsForUser:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetGeneralStatisticsForUserLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopEventHandlerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopEventHandler:32", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopEventHandlerLambdaFunctionQualifiedArn"}, {"OutputKey": "TrackLoginActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-trackLoginActivity:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-TrackLoginActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-CreateUser:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateUserLambdaFunctionQualifiedArn"}, {"OutputKey": "AddVideoWatchedLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddVideoWatched:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddVideoWatchedLambdaFunctionQualifiedArn"}, {"OutputKey": "HandleStudentStageActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-handleStudentStageActivity:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-HandleStudentStageActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-DeleteUser:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeleteUserLambdaFunctionQualifiedArn"}, {"OutputKey": "ListUsersByContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-listUsersByContract:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ListUsersByContractLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLessonMetricLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddLessonMetric:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddLessonMetricLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteDecksLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-deleteDecks:50", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeleteDecksLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updateContract:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateFlashcardDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createFlashcardDeck:59", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateFlashcardDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "ValidateTokenLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-validateToken:59", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ValidateTokenLambdaFunctionQualifiedArn"}, {"OutputKey": "GroupsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-groupsInterface:12", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GroupsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "TeacherGroupInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-teacherGroupInterface:49", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-TeacherGroupInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetCommentsByGroupId:14", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetCommentsByGroupIdLambdaFunctionQualifiedArn"}, {"OutputKey": "UserFetchPathsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-userFetchPaths:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UserFetchPathsLambdaFunctionQualifiedArn"}, {"OutputKey": "CoordinatorGroupInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-coordinatorGroupInterface:20", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CoordinatorGroupInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updateDeck:55", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "DeactivateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-deactivateContract:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeactivateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "ContractDomainsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-ContractDomainsInterface:26", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ContractDomainsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "GetDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getDeck:54", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "StudentGetSinglePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-studentGetSinglePath:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-StudentGetSinglePathLambdaFunctionQualifiedArn"}, {"OutputKey": "FindUserByIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindUserById:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindUserByIdLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoLike:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoComment:14", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "FileManagerImagesInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fileManagerImagesInterface:16", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FileManagerImagesInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "CompareSchoolsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-compareSchools:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CompareSchoolsLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSingleLessonWithMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetSingleLessonWithMetrics:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetSingleLessonWithMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "GetContractActivitySummaryLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getContractActivitySummary:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetContractActivitySummaryLambdaFunctionQualifiedArn"}, {"OutputKey": "CreatePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createPath:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreatePathLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopInventoryInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopInventoryInterface:44", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopInventoryInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FetchLessonMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fetchLessonMetrics:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FetchLessonMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createContract:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateFlashcardLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createFlashcard:59", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateFlashcardLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopInterface:38", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "RetrieveRewardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-retrieveRewards:50", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-RetrieveRewardsLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-UpdateUser:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateUserLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSelfFlashcardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getSelfFlashcards:59", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetSelfFlashcardsLambdaFunctionQualifiedArn"}, {"OutputKey": "OAuthInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-OAuthInterface:26", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-OAuthInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FindSchoolsByContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-findSchoolsByContract:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindSchoolsByContractLambdaFunctionQualifiedArn"}, {"OutputKey": "AuthorizerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-authorizer:63", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AuthorizerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveCommentLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveCommentLike:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveCommentLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "FetchQuestionMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fetchQuestionMetrics:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FetchQuestionMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "LoginUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-LoginUser:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-LoginUserLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdatePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updatePath:75", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdatePathLambdaFunctionQualifiedArn"}, {"OutputKey": "AddQuestionMetricLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddQuestionMetric:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddQuestionMetricLambdaFunctionQualifiedArn"}, {"OutputKey": "StudentFetchActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-studentFetchActivity:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-StudentFetchActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "ServiceEndpoint", "OutputValue": "https://bdxeubq1xf.execute-api.us-east-2.amazonaws.com/dev", "Description": "URL of the service endpoint", "ExportName": "sls-cds-service-dev-ServiceEndpoint"}, {"OutputKey": "ShopItemsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopItemsInterface:44", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopItemsInterfaceLambdaFunctionQualifiedArn"}], "Tags": [{"Key": "STAGE", "Value": "dev"}], "EnableTerminationProtection": false, "DriftInformation": {"StackDriftStatus": "NOT_CHECKED"}}]}