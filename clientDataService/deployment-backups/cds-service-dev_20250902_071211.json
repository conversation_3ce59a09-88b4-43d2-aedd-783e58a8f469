{"Stacks": [{"StackId": "arn:aws:cloudformation:us-east-2:010526255193:stack/cds-service-dev/a18abea0-87aa-11f0-b406-068b20c08893", "StackName": "cds-service-dev", "Description": "The AWS CloudFormation template for this Serverless application", "CreationTime": "2025-09-02T03:12:17.512000+00:00", "RollbackConfiguration": {}, "StackStatus": "CREATE_COMPLETE", "DisableRollback": false, "NotificationARNs": [], "Capabilities": ["CAPABILITY_IAM", "CAPABILITY_NAMED_IAM"], "Outputs": [{"OutputKey": "CommunicationInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-communicationInterface:14", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CommunicationInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FindLessonMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindLessonMetrics:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindLessonMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateSchoolLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-UpdateSchool:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateSchoolLambdaFunctionQualifiedArn"}, {"OutputKey": "BatchAnswerFlashcardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-batchAnswerFlashcards:61", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-BatchAnswerFlashcardsLambdaFunctionQualifiedArn"}, {"OutputKey": "FindQuestionMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindQuestionMetrics:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindQuestionMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "CalendarEventsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-calendarEventsInterface:29", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CalendarEventsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "CompareWithDistrictLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-compareWithDistrict:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CompareWithDistrictLambdaFunctionQualifiedArn"}, {"OutputKey": "ListSelfDecksLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-listSelfDecks:56", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ListSelfDecksLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoSaveLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoSave:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoSaveLambdaFunctionQualifiedArn"}, {"OutputKey": "ServerlessDeploymentBucketName", "OutputValue": "cds-service-dev-deployment-bucket", "ExportName": "sls-cds-service-dev-ServerlessDeploymentBucketName"}, {"OutputKey": "CreateSchoolLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-CreateSchool:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateSchoolLambdaFunctionQualifiedArn"}, {"OutputKey": "ClusterByOlympiadLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-ClusterByOlympiad:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ClusterByOlympiadLambdaFunctionQualifiedArn"}, {"OutputKey": "GetGeneralStatisticsForUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetGeneralStatisticsForUser:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetGeneralStatisticsForUserLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopEventHandlerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopEventHandler:34", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopEventHandlerLambdaFunctionQualifiedArn"}, {"OutputKey": "TrackLoginActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-trackLoginActivity:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-TrackLoginActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-CreateUser:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateUserLambdaFunctionQualifiedArn"}, {"OutputKey": "AddVideoWatchedLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddVideoWatched:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddVideoWatchedLambdaFunctionQualifiedArn"}, {"OutputKey": "HandleStudentStageActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-handleStudentStageActivity:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-HandleStudentStageActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-DeleteUser:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeleteUserLambdaFunctionQualifiedArn"}, {"OutputKey": "ListUsersByContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-listUsersByContract:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ListUsersByContractLambdaFunctionQualifiedArn"}, {"OutputKey": "AddLessonMetricLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddLessonMetric:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddLessonMetricLambdaFunctionQualifiedArn"}, {"OutputKey": "DeleteDecksLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-deleteDecks:52", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeleteDecksLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updateContract:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateFlashcardDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createFlashcardDeck:60", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateFlashcardDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "ValidateTokenLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-validateToken:60", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ValidateTokenLambdaFunctionQualifiedArn"}, {"OutputKey": "GroupsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-groupsInterface:14", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GroupsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "TeacherGroupInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-teacherGroupInterface:51", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-TeacherGroupInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "GetCommentsByGroupIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetCommentsByGroupId:16", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetCommentsByGroupIdLambdaFunctionQualifiedArn"}, {"OutputKey": "UserFetchPathsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-userFetchPaths:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UserFetchPathsLambdaFunctionQualifiedArn"}, {"OutputKey": "CoordinatorGroupInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-coordinatorGroupInterface:22", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CoordinatorGroupInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updateDeck:57", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "DeactivateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-deactivateContract:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-DeactivateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "ContractDomainsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-ContractDomainsInterface:28", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ContractDomainsInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "GetDeckLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getDeck:56", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetDeckLambdaFunctionQualifiedArn"}, {"OutputKey": "StudentGetSinglePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-studentGetSinglePath:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-StudentGetSinglePathLambdaFunctionQualifiedArn"}, {"OutputKey": "FindUserByIdLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-FindUserById:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindUserByIdLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoLike:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveVideoCommentLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveVideoComment:16", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveVideoCommentLambdaFunctionQualifiedArn"}, {"OutputKey": "FileManagerImagesInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fileManagerImagesInterface:18", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FileManagerImagesInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "CompareSchoolsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-compareSchools:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CompareSchoolsLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSingleLessonWithMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-GetSingleLessonWithMetrics:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetSingleLessonWithMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "GetContractActivitySummaryLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getContractActivitySummary:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetContractActivitySummaryLambdaFunctionQualifiedArn"}, {"OutputKey": "CreatePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createPath:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreatePathLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopInventoryInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopInventoryInterface:46", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopInventoryInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FetchLessonMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fetchLessonMetrics:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FetchLessonMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createContract:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateContractLambdaFunctionQualifiedArn"}, {"OutputKey": "CreateFlashcardLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-createFlashcard:61", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-CreateFlashcardLambdaFunctionQualifiedArn"}, {"OutputKey": "ShopInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopInterface:40", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "RetrieveRewardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-retrieveRewards:52", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-RetrieveRewardsLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdateUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-UpdateUser:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdateUserLambdaFunctionQualifiedArn"}, {"OutputKey": "GetSelfFlashcardsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-getSelfFlashcards:61", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-GetSelfFlashcardsLambdaFunctionQualifiedArn"}, {"OutputKey": "OAuthInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-OAuthInterface:27", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-OAuthInterfaceLambdaFunctionQualifiedArn"}, {"OutputKey": "FindSchoolsByContractLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-findSchoolsByContract:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FindSchoolsByContractLambdaFunctionQualifiedArn"}, {"OutputKey": "AuthorizerLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-authorizer:64", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AuthorizerLambdaFunctionQualifiedArn"}, {"OutputKey": "AddOrRemoveCommentLikeLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddOrRemoveCommentLike:78", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddOrRemoveCommentLikeLambdaFunctionQualifiedArn"}, {"OutputKey": "FetchQuestionMetricsLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-fetchQuestionMetrics:76", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-FetchQuestionMetricsLambdaFunctionQualifiedArn"}, {"OutputKey": "LoginUserLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-LoginUser:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-LoginUserLambdaFunctionQualifiedArn"}, {"OutputKey": "UpdatePathLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-updatePath:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-UpdatePathLambdaFunctionQualifiedArn"}, {"OutputKey": "AddQuestionMetricLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-AddQuestionMetric:79", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-AddQuestionMetricLambdaFunctionQualifiedArn"}, {"OutputKey": "StudentFetchActivityLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-studentFetchActivity:77", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-StudentFetchActivityLambdaFunctionQualifiedArn"}, {"OutputKey": "ServiceEndpoint", "OutputValue": "https://30zuu4x5bi.execute-api.us-east-2.amazonaws.com/dev", "Description": "URL of the service endpoint", "ExportName": "sls-cds-service-dev-ServiceEndpoint"}, {"OutputKey": "ShopItemsInterfaceLambdaFunctionQualifiedArn", "OutputValue": "arn:aws:lambda:us-east-2:010526255193:function:cds-service-dev-shopItemsInterface:46", "Description": "Current Lambda function version", "ExportName": "sls-cds-service-dev-ShopItemsInterfaceLambdaFunctionQualifiedArn"}], "Tags": [{"Key": "STAGE", "Value": "dev"}], "EnableTerminationProtection": false, "DriftInformation": {"StackDriftStatus": "NOT_CHECKED"}}]}