# Frontend Guide: Polling-based Chat Integration

This document explains how to integrate the new polling-first chat API in the Communication module. It covers polling loops, ETag usage for efficient caching, cursor management with lastReadOrder, and unread counters.

## Key Concepts

- Messages are ordered by a monotonically increasing `order` per channel.
- Clients fetch new messages by passing `afterOrder` to get only messages with `order > afterOrder`.
- When the user reads messages, POST `lastSeenOrder` to advance a server-side cursor (ChannelReadCursor).
- Endpoints return weak ETags. Send `If-None-Match` to receive `304 Not Modified` when there’s no change.
- Presence (online users) is optional and provided separately.

## Endpoints

- List user channels
  - GET `/communication/channels?type=CHAT|GROUP&limit=&offset=&sortBy=lastMessageAt&sortOrder=desc`
- Fetch channel messages (supports incremental polling)
  - GET `/communication/channels/{channelId}/messages?afterOrder=&limit=&sortBy=order&sortOrder=asc&includeDeleted=false`
  - Returns header `ETag` and body with `{ data: { messages: Message[], pagination, ... } }`
- Mark messages as viewed and advance read cursor
  - POST `/communication/channels/{channelId}/mark-viewed`
  - Body: `{ "lastSeenOrder": number }`
- Unread info (for badge counters)
  - GET `/communication/unread-messages?channelId=optional`
  - Returns header `ETag` computed from unreadCount
- Unread list (optional detailed listing)
  - GET `/communication/channels/{channelId}/unread-messages?limit=&offset=`
- Presence (optional)
  - POST `/communication/channels/{channelId}/presence` with `{ status: 'online'|'away'|'offline' }`
  - GET `/communication/channels/{channelId}/online-users`
- Send message
  - POST `/communication/messages` (JSON or multipart/form-data). JSON body for text: `{ text, channelId, messageType: 'text', replyTo?, metadata? }`

## ETag Usage

- On a messages fetch, read `ETag` from the response headers and cache it per (channelId, afterOrder range).
- Next request: include `If-None-Match: <etag>`. If server returns `304`, keep existing list without parsing a body.
- Compute a stable key on the client per channel for the last ETag you saw when `afterOrder` was your latest known order.

Example (fetch):

```ts
const res = await fetch(url, { headers: { 'If-None-Match': lastEtag || '' } });
if (res.status === 304) return { messages: [], etag: lastEtag };
const etag = res.headers.get('ETag') || undefined;
const payload = await res.json();
return { messages: payload.data.messages, etag };
```

## Cursor Management

- Track `lastKnownOrder` per channel (max order you have locally).
- When user views messages, POST `lastSeenOrder = lastKnownOrder` (or a precise order you deem read) to advance the server-side cursor.
- Unread counters are derived server-side using the cursor, so always advance it promptly after reads.

## Recommended Polling Strategy

- Foreground (chat screen visible): poll messages every 3–5s.
- Background (tab not focused or channel not open): poll every 15–30s or stop; instead poll unread info endpoint for badges.
- Always apply backoff on failures (e.g., exponential up to 60s) and stop polling when offline.
- Use `afterOrder` with your `lastKnownOrder` to fetch only deltas.
- Use ETags to avoid payload transfer when unchanged.

## End-to-end Example (TypeScript)

```ts
class ChatPollingClient {
  private baseUrl: string;
  private authHeaders: () => Record<string, string>;
  private etags = new Map<string, string>(); // key: `msgs:${channelId}`
  private lastOrder = new Map<string, number>(); // key: channelId

  constructor(baseUrl: string, authHeaders: () => Record<string, string>) {
    this.baseUrl = baseUrl;
    this.authHeaders = authHeaders;
  }

  getLastOrder(channelId: string) {
    return this.lastOrder.get(channelId) || 0;
  }

  setLastOrder(channelId: string, order: number) {
    const prev = this.getLastOrder(channelId);
    if (order > prev) this.lastOrder.set(channelId, order);
  }

  private etagKey(channelId: string) {
    return `msgs:${channelId}`;
  }

  async fetchNewMessages(channelId: string) {
    const afterOrder = this.getLastOrder(channelId);
    const url = new URL(`${this.baseUrl}/communication/channels/${channelId}/messages`);
    url.searchParams.set('afterOrder', String(afterOrder));

    const key = this.etagKey(channelId);
    const etag = this.etags.get(key);

    const res = await fetch(url.toString(), {
      headers: { ...this.authHeaders(), ...(etag ? { 'If-None-Match': etag } : {}) },
    });

    if (res.status === 304) {
      return [] as any[];
    }

    if (!res.ok) throw new Error(`Fetch messages failed: ${res.status}`);

    const newEtag = res.headers.get('ETag') || undefined;
    if (newEtag) this.etags.set(key, newEtag);

    const payload = await res.json();
    const messages = payload.data?.messages || [];
    for (const m of messages) this.setLastOrder(channelId, m.order || 0);
    return messages as any[];
  }

  async markViewed(channelId: string, order?: number) {
    const lastSeenOrder = order ?? this.getLastOrder(channelId);
    const res = await fetch(`${this.baseUrl}/communication/channels/${channelId}/mark-viewed`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', ...this.authHeaders() },
      body: JSON.stringify({ lastSeenOrder }),
    });
    if (!res.ok) throw new Error(`mark-viewed failed: ${res.status}`);
    // Optionally parse response and confirm lastReadOrder
    return res.json().catch(() => ({}));
  }

  async getUnreadInfo(channelId?: string) {
    const url = new URL(`${this.baseUrl}/communication/unread-messages`);
    if (channelId) url.searchParams.set('channelId', channelId);

    const key = `unread:${channelId || 'all'}`;
    const etag = this.etags.get(key);

    const res = await fetch(url.toString(), {
      headers: { ...this.authHeaders(), ...(etag ? { 'If-None-Match': etag } : {}) },
    });

    if (res.status === 304) {
      return null; // no change
    }

    if (!res.ok) throw new Error(`unread-info failed: ${res.status}`);

    const newEtag = res.headers.get('ETag') || undefined;
    if (newEtag) this.etags.set(key, newEtag);

    const payload = await res.json();
    return payload.data; // { unreadCount, firstUnreadMessage, hasUnreadMessages }
  }
}
```

### Poll Loop Example (React)

```ts
useEffect(() => {
  let stopped = false;
  let timer: any;

  async function tick() {
    try {
      const msgs = await client.fetchNewMessages(channelId);
      if (!stopped && msgs.length) appendMessages(msgs);
    } catch (e) {
      console.warn('poll error', e);
    } finally {
      if (!stopped) timer = setTimeout(tick, 4000);
    }
  }

  tick();
  return () => { stopped = true; clearTimeout(timer); };
}, [channelId]);
```

### Mark Viewed When User Scrolls To Bottom

```ts
async function onReachedEnd(channelId: string, lastVisibleOrder: number) {
  try { await client.markViewed(channelId, lastVisibleOrder); }
  catch (e) { console.warn('mark-viewed failed', e); }
}
```

## Migration From Event-based Implementation

- Remove any listener logic relying on a `/events` endpoint or Redis event stream.
- Replace with:
  1) A periodic poll calling `GET /communication/channels/{channelId}/messages?afterOrder=...`
  2) Use returned `ETag` and send it back via `If-None-Match` for subsequent polls
  3) After rendering newly fetched messages, call `POST /communication/channels/{channelId}/mark-viewed` with your last seen order
  4) For global badges, periodically call `GET /communication/unread-messages` (with `If-None-Match`) to avoid parsing payload if unchanged

## Error Handling & Backoff

- If a fetch fails (5xx or network), back off exponentially (e.g., 4s, 8s, 16s up to 60s).
- When resuming from background, do an immediate poll.
- Respect 304 to reduce bandwidth and CPU.

## Performance Notes

- Keep local max `order` per channel and use it as `afterOrder`.
- Normalize `order` to numbers; defensive parse with `Number()` if needed.
- Consider a per-channel AbortController if you allow channel switching mid-flight.
- Tune polling interval: shorter when the user is actively typing/reading; longer otherwise.

## Security & Auth

- Send authenticated requests; all endpoints require `authorizer` context.
- Only members of a channel can fetch/mark messages for that channel.

## FAQ

- Q: What if I miss a poll? A: You won’t lose messages—next poll with `afterOrder` will fetch all newer ones.
- Q: Is ETag required? A: Strongly recommended. It reduces payload and lets the API return 304 quickly.
- Q: How to initialize afterOrder? A: Start at 0 for a new channel, or fetch without `afterOrder` once to get recent history and record the last message `order`.