import CommunicationApi from './CommunicationApi';

export type PollingIntervals = {
  foregroundMs: number;
  backgroundMs: number;
  maxBackoffMs: number;
};

export type ChatPollingCallbacks = {
  onMessages: (channelId: string, messages: any[]) => void;
  onUnread: (info: { unreadCount: number; perChannel?: Record<string, number> }) => void;
  onPerChannelUnread?: (perChannel: Record<string, number>) => void;
  onError?: (err: any) => void;
};

export class ChatPollingClient {
  private etags = new Map<string, string>();
  private lastOrder = new Map<string, number>();
  private timers = new Map<string, any>();
  private unreadTimer: any = null;
  private running = false;
  private backoffMs = 0;

  constructor(private intervals: PollingIntervals, private callbacks: ChatPollingCallbacks) {}

  getLastOrder(channelId: string) { return this.lastOrder.get(channelId) || 0; }
  setLastOrder(channelId: string, order: number) {
    const prev = this.getLastOrder(channelId);
    if (order > prev) this.lastOrder.set(channelId, order);
  }

  private etagKeyMsgs(channelId: string) { return `msgs:${channelId}`; }
  private etagKeyUnread() { return `unread`; }

  async pollChannel(channelId: string, foreground = true) {
    try {
      const etagKey = this.etagKeyMsgs(channelId);
      const etag = this.etags.get(etagKey);
      const afterOrder = this.getLastOrder(channelId) || undefined;
      const res = await CommunicationApi.fetchMessages(channelId, { afterOrder, sortBy: 'order', sortOrder: 'asc', includeDeleted: false }, etag);
      if (res.status === 200) {
        const et = res.headers['etag'] as string | undefined;
        if (et) this.etags.set(etagKey, et);
        const body = res.data;
        const messages = body?.data?.messages || [];
        if (messages.length) {
          const maxOrder = Math.max(...messages.map((m: any) => m.order || 0));
          if (maxOrder) this.setLastOrder(channelId, maxOrder);
          this.callbacks.onMessages(channelId, messages);
        }
      }
      this.backoffMs = 0; // reset on success
    } catch (err) {
      this.backoffMs = Math.min(this.intervals.maxBackoffMs, this.backoffMs ? this.backoffMs * 2 : 2000);
      this.callbacks.onError?.(err);
    } finally {
      if (this.running) {
        const isForeground = typeof document !== 'undefined' ? (document.visibilityState === 'visible') : foreground;
        const interval = this.backoffMs || (navigator.onLine ? (isForeground ? this.intervals.foregroundMs : this.intervals.backgroundMs) : this.intervals.backgroundMs);
        const t = setTimeout(() => this.pollChannel(channelId, isForeground), interval);
        this.timers.set(channelId, t);
      }
    }
  }

  stopChannel(channelId: string) {
    const t = this.timers.get(channelId);
    if (t) clearTimeout(t);
    this.timers.delete(channelId);
  }

  async pollUnread() {
    try {
      const etagKey = this.etagKeyUnread();
      const etag = this.etags.get(etagKey);
      const res = await CommunicationApi.unreadInfo(undefined, etag);
      if (res.status === 200) {
        const et = res.headers['etag'] as string | undefined;
        if (et) this.etags.set(etagKey, et);
        const data = res.data?.data;
        if (data) {
          this.callbacks.onUnread(data);
          if (data.perChannel && this.callbacks.onPerChannelUnread) {
            this.callbacks.onPerChannelUnread(data.perChannel);
          }
        }
      }
      this.backoffMs = 0;
    } catch (err) {
      this.backoffMs = Math.min(this.intervals.maxBackoffMs, this.backoffMs ? this.backoffMs * 2 : 2000);
      this.callbacks.onError?.(err);
    } finally {
      if (this.running) {
        const interval = this.backoffMs || (navigator.onLine ? this.intervals.backgroundMs : this.intervals.backgroundMs);
        this.unreadTimer = setTimeout(() => this.pollUnread(), interval);
      }
    }
  }

  start(channelId?: string) {
    this.running = true;
    if (channelId) this.pollChannel(channelId, true);
    this.pollUnread();
  }

  stop() {
    this.running = false;
    for (const [, t] of this.timers) clearTimeout(t);
    this.timers.clear();
    if (this.unreadTimer) clearTimeout(this.unreadTimer);
    this.unreadTimer = null;
  }

  async advanceReadCursor(channelId: string) {
    const last = this.getLastOrder(channelId);
    if (last) await CommunicationApi.markViewed(channelId, last);
  }
}

export default ChatPollingClient;

