export type ChannelType = 'CHAT' | 'GROUP';

export interface ChannelMember {
  id: string;
  name: string;
  avatarUrl?: string;
  role?: 'owner' | 'admin' | 'member';
  presence?: PresenceStatus;
}

export type PresenceStatus = 'online' | 'away' | 'offline';

export interface Channel {
  id: string;
  type: ChannelType;
  name: string;
  lastMessageAt?: string; // ISO
  members: ChannelMember[];
}

export interface ChannelsListResponse {
  data: {
    channels: Channel[];
    pagination?: {
      limit?: number;
      offset?: number;
      total?: number;
    };
  };
}

export interface UnreadInfoResponse {
  data: {
    unreadCount: number;
    perChannel?: Record<string, number>;
  };
}

export interface PresenceUsersResponse {
  data: {
    onlineUsers: Array<{ id: string; status: PresenceStatus }>;
  };
}

