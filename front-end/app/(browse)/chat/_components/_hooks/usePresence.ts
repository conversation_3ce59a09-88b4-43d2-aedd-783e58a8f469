import { useEffect, useRef, useState, useCallback } from 'react'
import Communication<PERSON>pi from '@/services/COMMUNICATION/api/CommunicationApi'
import type { PresenceStatus } from '@/services/COMMUNICATION/interfaces/channel'

export interface UsePresenceOptions {
  channelId?: string
  heartbeatIntervalMs?: number
  awayTimeoutMs?: number
}

export function usePresence({ 
  channelId, 
  heartbeatIntervalMs = 30000, // 30s heartbeat
  awayTimeoutMs = 60000 // 1min without activity = away
}: UsePresenceOptions = {}) {
  const [status, setStatus] = useState<PresenceStatus>('online')
  const [onlineUsers, setOnlineUsers] = useState<Array<{ id: string; status: PresenceStatus }>>([])
  const lastActivityRef = useRef<number>(Date.now())
  const heartbeatTimerRef = useRef<NodeJS.Timeout>()
  const awayTimerRef = useRef<NodeJS.Timeout>()

  // Update last activity timestamp
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now()
    if (status === 'away') {
      setStatus('online')
    }
  }, [status])

  // Send presence heartbeat to server
  const sendHeartbeat = useCallback(async (presenceStatus: PresenceStatus) => {
    if (!channelId) return
    try {
      await CommunicationApi.presenceHeartbeat(channelId, presenceStatus)
    } catch (err) {
      console.warn('Presence heartbeat failed:', err)
    }
  }, [channelId])

  // Fetch online users for the channel
  const fetchOnlineUsers = useCallback(async () => {
    if (!channelId) return
    try {
      const response = await CommunicationApi.getOnlineUsers(channelId)
      setOnlineUsers(response.data.onlineUsers || [])
    } catch (err) {
      console.warn('Failed to fetch online users:', err)
    }
  }, [channelId])

  // Check if user should be marked as away
  const checkAwayStatus = useCallback(() => {
    const timeSinceActivity = Date.now() - lastActivityRef.current
    if (timeSinceActivity >= awayTimeoutMs && status === 'online') {
      setStatus('away')
    }
  }, [awayTimeoutMs, status])

  // Set up activity listeners
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    
    events.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity)
      })
    }
  }, [updateActivity])

  // Set up visibility change listener
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        setStatus('away')
      } else {
        updateActivity()
        setStatus('online')
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [updateActivity])

  // Set up heartbeat timer
  useEffect(() => {
    if (!channelId) return

    const startHeartbeat = () => {
      // Send initial heartbeat
      sendHeartbeat(status)
      
      // Set up periodic heartbeat
      heartbeatTimerRef.current = setInterval(() => {
        checkAwayStatus()
        sendHeartbeat(status)
      }, heartbeatIntervalMs)
    }

    startHeartbeat()

    return () => {
      if (heartbeatTimerRef.current) {
        clearInterval(heartbeatTimerRef.current)
      }
    }
  }, [channelId, status, heartbeatIntervalMs, sendHeartbeat, checkAwayStatus])

  // Set up away status checker
  useEffect(() => {
    awayTimerRef.current = setInterval(checkAwayStatus, 10000) // Check every 10s

    return () => {
      if (awayTimerRef.current) {
        clearInterval(awayTimerRef.current)
      }
    }
  }, [checkAwayStatus])

  // Fetch online users periodically
  useEffect(() => {
    if (!channelId) return

    fetchOnlineUsers() // Initial fetch

    const interval = setInterval(fetchOnlineUsers, 30000) // Every 30s
    return () => clearInterval(interval)
  }, [channelId, fetchOnlineUsers])

  // Send offline status when component unmounts
  useEffect(() => {
    return () => {
      if (channelId) {
        CommunicationApi.presenceHeartbeat(channelId, 'offline').catch(() => {})
      }
    }
  }, [channelId])

  return {
    status,
    onlineUsers,
    updateActivity,
    fetchOnlineUsers
  }
}
