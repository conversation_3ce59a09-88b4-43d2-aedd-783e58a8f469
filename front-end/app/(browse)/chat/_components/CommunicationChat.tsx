"use client"
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import ChatPolling<PERSON>lient from '@/services/COMMUNICATION/api/ChatPollingClient'
import Communication<PERSON>pi from '@/services/COMMUNICATION/api/CommunicationApi'
import type { Channel } from '@/services/COMMUNICATION/interfaces/channel'
import type { Message } from '@/services/COMMUNICATION/interfaces/message'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'

import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { toast } from '@/hooks/use-toast'
import { Image as ImageIcon, SmilePlus, Send } from 'lucide-react'
import dynamic from 'next/dynamic'
import { sanitizeMessage } from './_utils/messageSanitizer'
import { usePresence } from './_hooks/usePresence'
import { PresenceIndicator, PresenceList } from './PresenceIndicator'

// @ts-ignore - ReactMarkdown type issues with dynamic import
const ReactMarkdown = dynamic(() => import('react-markdown'), { ssr: false })

export default function CommunicationChat() {
  const [channels, setChannels] = useState<Channel[]>([])
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null)
  const [messages, setMessages] = useState<Record<string, Message[]>>({})
  const [input, setInput] = useState('')
  const [uploading, setUploading] = useState(false)
  const [unreadCount, setUnreadCount] = useState(0)
  const [perChannelUnread, setPerChannelUnread] = useState<Record<string, number>>({})
  const containerRef = useRef<HTMLDivElement | null>(null)

  // Presence management for the selected channel
  const { status: userPresenceStatus, onlineUsers, updateActivity } = usePresence({
    channelId: selectedChannel?.id,
    heartbeatIntervalMs: 30000,
    awayTimeoutMs: 60000
  })

  const polling = useMemo(() => new ChatPollingClient({ foregroundMs: 4000, backgroundMs: 20000, maxBackoffMs: 60000 }, {
    onMessages: (channelId, newMsgs) => {
      setMessages(prev => {
        const merged = [...(prev[channelId] || []), ...newMsgs]
          .reduce((acc: Message[], m: any) => {
            const exists = acc.find(x => x.id === m.id)
            if (!exists) acc.push(m)
            return acc
          }, [])
          .sort((a, b) => (a.order || 0) - (b.order || 0))
        return { ...prev, [channelId]: merged }
      })
      // Auto-scroll on new messages if viewing this channel
      if (selectedChannel && selectedChannel.id === channelId) {
        setTimeout(() => containerRef.current?.scrollTo({ top: containerRef.current.scrollHeight, behavior: 'smooth' }), 50)
      }
    },
    onUnread: (info) => {
      setUnreadCount(info.unreadCount || 0)
      if (info.perChannel) {
        setPerChannelUnread(info.perChannel)
      }
    },
    onPerChannelUnread: (perChannel) => {
      setPerChannelUnread(perChannel)
    },
    onError: (err) => {
      console.error('Chat polling error', err)
      toast({ title: 'Problema de conexão', description: 'Tentando reconectar ao chat...' })
    }
  }), [])

  useEffect(() => {
    let mounted = true
    const init = async () => {
      try {
        const res = await CommunicationApi.listChannels({ sortBy: 'lastMessageAt', sortOrder: 'desc' })
        if (res.status === 200) {
          const list = res.data?.data?.channels || []
          if (!mounted) return
          setChannels(list)
          if (list[0]) setSelectedChannel(list[0])
        }
      } catch (e) {
        console.error(e)
        toast({ title: 'Erro', description: 'Não foi possível carregar seus canais.' })
      }
    }
    init()
    polling.start()
    return () => {
      mounted = false
      polling.stop()
    }
  }, [polling])

  useEffect(() => {
    if (!selectedChannel) return
    // start focused polling for this channel only
    polling.pollChannel(selectedChannel.id, true)
    return () => {
      polling.stopChannel(selectedChannel.id)
    }
  }, [selectedChannel])

  const sendText = async () => {
    if (!selectedChannel || !input.trim()) return
    const chId = selectedChannel.id
    // optimistic message
    const temp: Message = {
      id: `temp-${Date.now()}`,
      channelId: chId,
      order: (messages[chId]?.[messages[chId].length - 1]?.order || 0) + 0.0001,
      type: 'text',
      text: input,
      author: { id: 'me', name: 'Você' },
      createdAt: new Date().toISOString(),
    }
    setMessages(prev => ({ ...prev, [chId]: [...(prev[chId] || []), temp] }))
    setInput('')
    try {
      const res = await CommunicationApi.sendMessageJson({ channelId: chId, messageType: 'text', text: temp.text || '' })
      // replace temp with real
      const real = res.data.message
      setMessages(prev => ({
        ...prev,
        [chId]: (prev[chId] || []).map(m => m.id === temp.id ? real as any : m)
      }))
      // advance read cursor since user sees their own message
      polling.advanceReadCursor(chId)

      // Clear unread count for this channel since user is actively participating
      setPerChannelUnread(prev => ({
        ...prev,
        [chId]: 0
      }))
    } catch (e) {
      toast({ title: 'Falha ao enviar', description: 'Sua mensagem não foi enviada. Tentaremos novamente.' })
    }
  }

  const onChannelClick = async (ch: Channel) => {
    setSelectedChannel(ch)
    // ensure initial fetch
    const existing = messages[ch.id]?.length || 0
    if (!existing) {
      await polling.pollChannel(ch.id, true)
    }
    // mark viewed and clear unread count for this channel
    await polling.advanceReadCursor(ch.id)

    // Optimistically clear unread count for this channel
    setPerChannelUnread(prev => ({
      ...prev,
      [ch.id]: 0
    }))
  }

  return (
    <div className="h-[calc(100vh-4rem)] grid grid-cols-12 gap-4 p-4 bg-gradient-to-b from-[#040A2F] to-[#0F2057] text-blue-100">
      <div className="col-span-12 md:col-span-4 lg:col-span-3 bg-blue-800/20 rounded-xl border border-blue-600/30 backdrop-blur-md overflow-hidden">
        <div className="px-4 py-3 border-b border-blue-600/30 flex items-center justify-between">
          <div className="font-semibold">Conversas</div>
          {unreadCount > 0 && <Badge className="bg-blue-600/50">{unreadCount}</Badge>}
        </div>
        <div className="divide-y divide-blue-600/20 max-h-[calc(100%-3rem)] overflow-y-auto">
          {channels.map(ch => {
            const channelUnreadCount = perChannelUnread[ch.id] || 0;
            return (
              <button key={ch.id} onClick={() => onChannelClick(ch)} className={`w-full text-left px-4 py-3 hover:bg-blue-800/30 transition ${selectedChannel?.id===ch.id? 'bg-blue-800/20':''}`}>
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={ch.members[0]?.avatarUrl} />
                      <AvatarFallback>{ch.name?.[0]||'C'}</AvatarFallback>
                    </Avatar>
                    {channelUnreadCount > 0 && (
                      <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-blue-600/80 text-white border-blue-400/50">
                        {channelUnreadCount > 99 ? '99+' : channelUnreadCount}
                      </Badge>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium flex items-center gap-2">
                      <span>{ch.name}</span>
                      {channelUnreadCount > 0 && selectedChannel?.id !== ch.id && (
                        <Badge className="bg-blue-600/50 text-xs px-1.5 py-0.5">
                          {channelUnreadCount}
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-blue-300 flex items-center gap-2">
                      <span>{ch.type==='GROUP'?'Grupo':'Chat'}</span>
                      {ch.members && ch.members.length > 0 && (
                        <PresenceList
                          users={ch.members.map(m => ({
                            id: m.id,
                            status: m.presence || 'offline',
                            name: m.name
                          }))}
                          maxVisible={2}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </button>
            )
          })}
          {channels.length===0 && (
            <div className="p-4 text-sm text-blue-300">Nenhum canal encontrado.</div>
          )}
        </div>
      </div>

      <div className="col-span-12 md:col-span-8 lg:col-span-9 bg-blue-800/20 rounded-xl border border-blue-600/30 backdrop-blur-md overflow-hidden flex flex-col">
        <div className="px-4 py-3 border-b border-blue-600/30 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="font-semibold">{selectedChannel?.name || 'Selecione um canal'}</div>
            <PresenceIndicator status={userPresenceStatus} size="sm" />
          </div>
          <div className="flex items-center gap-2">
            <PresenceList users={onlineUsers.map(u => ({ ...u, name: u.id }))} maxVisible={3} />
            <div className="text-xs text-blue-300">{selectedChannel?.members?.length || 0} membros</div>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto" ref={containerRef}>
          <div className="p-4 space-y-3">
            <AnimatePresence initial={false}>
              {(messages[selectedChannel?.id||'']||[]).map((m) => (
                <motion.div key={m.id} initial={{ opacity: 0, y: 10 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0 }} className={`flex ${m.author.id==='me'?'justify-end':'justify-start'}`}>
                  <div className={`max-w-[85%] p-3 rounded-2xl ${m.author.id==='me'?'bg-blue-600/50 text-white rounded-tr-sm border border-blue-400/30':'bg-blue-800/50 text-blue-100 rounded-tl-sm border border-blue-500/30'}`}>
                    {m.type==='text' && (
                      <ReactMarkdown>{sanitizeMessage(m.text || '')}</ReactMarkdown>
                    )}
                    {m.attachments?.map(att => (
                      <div key={att.id} className="mt-2">
                        {att.mimeType.startsWith('image/') ? (
                          // eslint-disable-next-line @next/next/no-img-element
                          <img src={att.url} alt={att.name} className="rounded-lg border border-blue-500/30" />
                        ) : (
                          <a href={att.url} target="_blank" rel="noreferrer" className="underline text-blue-300">{att.name}</a>
                        )}
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
        <div className="p-3 border-t border-blue-600/30">
          <div className="flex items-center gap-2">
            <label className="cursor-pointer text-blue-300">
              <input type="file" accept="image/*,application/pdf" className="hidden" onChange={async (e)=>{
                const file = e.target.files?.[0];
                if (!file || !selectedChannel) return;
                setUploading(true);
                try {
                  // Use existing uploader service
                  const { default: FileController } = await import('@/services/FILE_UPLOADER/api/FileController');
                  const uploadRes = await FileController.uploadFile(file);
                  const form = new FormData();
                  form.append('channelId', selectedChannel.id);
                  form.append('messageType', file.type.startsWith('image/') ? 'image' : 'file');
                  form.append('fileUrl', uploadRes?.data?.url || '');
                  form.append('fileName', file.name);
                  form.append('mimeType', file.type);
                  await CommunicationApi.sendMessageMultipart(form);
                } catch (err) {
                  console.error(err);
                  toast({ title: 'Falha no upload', description: 'Não foi possível enviar o arquivo.' })
                } finally {
                  setUploading(false);
                }
              }} />
              <Button asChild variant="ghost" className="text-blue-300" title="Anexar imagem" disabled={uploading}>
                <span><ImageIcon size={18} /></span>
              </Button>
            </label>
            <Button variant="ghost" className="text-blue-300" title="Emoji">
              <SmilePlus size={18} />
            </Button>
            <Input
              placeholder="Escreva uma mensagem..."
              value={input}
              onChange={(e) => {
                setInput(e.target.value)
                updateActivity() // Update presence on typing
              }}
              onKeyDown={(e)=>{
                if(e.key==='Enter') sendText()
                updateActivity() // Update presence on key press
              }}
              className="flex-1 bg-blue-900/30 border-blue-600/30 text-blue-100 placeholder-blue-300"
            />
            <Button onClick={sendText} className="bg-blue-600/60 hover:bg-blue-600/70">
              <Send size={18} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

