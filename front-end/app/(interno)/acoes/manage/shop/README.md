# Shop Management

This module provides administrative tools for managing the global shop and pending orders.

## Features

### 1. Create New Items
- **Location**: `/acoes/manage/shop` → "Criar Novo Item" tab
- **Purpose**: Add new items to the global shop inventory
- **Fields**:
  - Basic Information: Name, Description, Category, Type, Usage Type
  - Pricing: Base Price, Current Price, Discount
  - Inventory: Stock Quantity, SKU
  - Options: Automatic Processing

### 2. Manage Pending Orders
- **Location**: `/acoes/manage/shop` → "Pedidos Pendentes" tab
- **Purpose**: View and update the status of user purchases
- **Features**:
  - Search orders by name or description
  - Filter by status (Pending, Processing, Completed, Cancelled)
  - Update order status with action buttons
  - Real-time status updates

## API Integration

The module integrates with the SHOP service API:

- **Create Items**: Uses `ShopAPI.createItems()` to add new items to the global shop
- **Pending Orders**: Displays orders with "Pending" status for administrative review
- **Status Updates**: Allows admins to change order status (Pending → Processing → Completed)

## Navigation

Access the shop management from:
1. Go to `/acoes` (Internal Actions)
2. Click on "Gerenciar Loja" card
3. Use the tabs to switch between creating items and managing orders

## Status Flow

Orders follow this status progression:
1. **Pending** - Initial state when user makes a purchase
2. **Processing** - Admin has started processing the order
3. **Completed** - Order has been fulfilled
4. **Cancelled** - Order was cancelled (optional)

## Technical Notes

- Uses the existing SHOP service interfaces and API
- Implements proper form validation and error handling
- Follows the existing design system with blue/purple theme
- Responsive design for mobile and desktop
- Toast notifications for user feedback
