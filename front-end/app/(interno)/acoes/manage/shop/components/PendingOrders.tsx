"use client"

import { useState, useEffect } from 'react'
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { ClockIcon, SearchIcon, FilterIcon, CheckIcon, XIcon, GiftIcon } from 'lucide-react'
import { IInventoryItem } from "@/services/SHOP/interfaces/inventoryItem"
import ShopAPI from "@/services/SHOP/api"
import UpdateGiftCardModal from './UpdateGiftCardModal'

const ORDER_STATUSES = [
  { value: 'Pending', label: 'Pendente', color: 'bg-yellow-500' },
  { value: 'PaymentProcessing', label: 'Processando Pagamento', color: 'bg-orange-500' },
  { value: 'Processing', label: 'Processando', color: 'bg-blue-500' },
  { value: 'Completed', label: 'Concluí<PERSON>', color: 'bg-green-500' },
  { value: 'Cancelled', label: 'Cancelado', color: 'bg-red-500' }
]

export default function PendingOrders() {
  const { toast } = useToast()
  const [orders, setOrders] = useState<IInventoryItem[]>([])
  const [filteredOrders, setFilteredOrders] = useState<IInventoryItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('Pending')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [giftCardModalOpen, setGiftCardModalOpen] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<IInventoryItem | null>(null)

  useEffect(() => {
    fetchPendingOrders()
  }, [])

  useEffect(() => {
    filterOrders()
  }, [orders, searchTerm])

  useEffect(() => {
    // When status filter changes, fetch new data
    fetchPendingOrders(1)
  }, [statusFilter])

  const fetchPendingOrders = async (page = 1) => {
    try {
      setIsLoading(true)
      
      // Call the real SHOP API to get pending orders
      const response = await ShopAPI.getInventoryItemsByStatus({
        status: statusFilter,
        limit: 20,
        page: page
      })
      
      
      
      // Add fallback for unexpected response structure
      if (response && response.items) {
        setOrders(response.items)
        setCurrentPage(response.pagination?.currentPage || 1)
        setTotalPages(response.pagination?.totalPages || 1)
        setTotalItems(response.pagination?.totalItems || 0)
      } else {
        console.warn('Unexpected API response structure:', response)
        setOrders([])
        setCurrentPage(1)
        setTotalPages(1)
        setTotalItems(0)
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
      toast({
        title: "Erro",
        description: "Erro ao carregar pedidos pendentes.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const filterOrders = () => {
    // First filter out orders without itemId (which contains the item info)
    const validOrders = orders.filter(order => order.itemId)
    
    let filtered = validOrders.filter(order => {
      const matchesSearch = order.itemId!.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           order.itemId!.description?.toLowerCase().includes(searchTerm.toLowerCase())
      return matchesSearch
    })
    setFilteredOrders(filtered)
  }

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      // This would call the SHOP API to update the order status
      // For now, we'll update the local state
             setOrders(prev => prev.map(order => 
         order._id === orderId 
           ? { ...order, status: newStatus }
           : order
       ))

      toast({
        title: "Sucesso!",
        description: `Status do pedido atualizado para ${newStatus}.`,
        variant: "default"
      })
    } catch (error) {
      console.error('Error updating order status:', error)
      toast({
        title: "Erro",
        description: "Erro ao atualizar status do pedido.",
        variant: "destructive"
      })
    }
  }

  const getStatusColor = (status: string) => {
    const statusConfig = ORDER_STATUSES.find(s => s.value === status)
    return statusConfig?.color || 'bg-gray-500'
  }

  const getStatusLabel = (status: string) => {
    const statusConfig = ORDER_STATUSES.find(s => s.value === status)
    return statusConfig?.label || status
  }

  const openGiftCardModal = (order: IInventoryItem) => {
    setSelectedOrder(order)
    setGiftCardModalOpen(true)
  }

  const closeGiftCardModal = () => {
    setGiftCardModalOpen(false)
    setSelectedOrder(null)
  }

  const handleGiftCardUpdate = () => {
    // Refresh the orders list after gift card update
    fetchPendingOrders(currentPage)
  }

  if (isLoading) {
    return (
      <Card className="p-6 bg-blue-950/10 border-blue-500/20">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-blue-300">Carregando pedidos...</span>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6 bg-blue-950/10 border-blue-500/20">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold text-white">Pedidos Pendentes</h3>
            <p className="text-blue-300 text-sm">
              {filteredOrders.length} pedido(s) encontrado(s)
            </p>
          </div>
          
          <Button
            onClick={() => fetchPendingOrders(1)}
            variant="outline"
            size="sm"
            className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
          >
            <ClockIcon className="w-4 h-4 mr-2" />
            Atualizar
          </Button>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label className="text-blue-300 text-sm">Buscar</Label>
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400" />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por nome ou descrição..."
                className="pl-10 bg-blue-950/30 border-blue-500/30 text-white"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-blue-300 text-sm">Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-blue-950/30 border-blue-500/30 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-blue-950 border-blue-500">
                {ORDER_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <ClockIcon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <p className="text-blue-300">Nenhum pedido encontrado</p>
            </div>
          ) : (
                         filteredOrders.map((order) => (
               <Card key={order._id} className="p-4 bg-blue-950/20 border-blue-500/20">
                <div className="flex flex-col lg:flex-row justify-between gap-4">
                  <div className="flex-1">
                                         <div className="flex items-start justify-between mb-2">
                       <h4 className="font-medium text-white">{order.itemId?.name || 'Nome não disponível'}</h4>
                       <Badge className={getStatusColor(order.status)}>
                         {getStatusLabel(order.status)}
                       </Badge>
                     </div>
                     
                     <p className="text-blue-300 text-sm mb-2">
                       {order.itemId?.description || 'Descrição não disponível'}
                     </p>
                    
                                         <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
                       <div>
                         <span className="text-blue-400">Quantidade:</span>
                         <p className="text-white">{order.quantity}</p>
                       </div>
                       <div>
                         <span className="text-blue-400">Preço:</span>
                         <p className="text-white">{order.buyedPrice.toFixed(2)}</p>
                       </div>
                       <div>
                         <span className="text-blue-400">Categoria:</span>
                         <p className="text-white capitalize">{order.itemId?.category || 'N/A'}</p>
                       </div>
                       <div>
                         <span className="text-blue-400">Tipo:</span>
                         <p className="text-white">{order.itemId?.type || 'N/A'}</p>
                       </div>
                     </div>
                     
                     <div className="mt-2 text-xs text-blue-400">
                       <span>Atualizado em: {new Date(order.itemId?.updatedAt || '').toLocaleString('pt-BR')}</span>
                     </div>
                  </div>

                                     <div className="flex flex-col gap-2">
                     {order.status === 'Pending' && (
                       <>
                         {order.itemId?.category === 'GiftCard' && order.itemId?.type === 'GiftCard' && (
                           <Button
                             size="sm"
                             variant="outline"
                             onClick={() => openGiftCardModal(order)}
                             className="border-purple-500/30 text-purple-300 hover:bg-purple-950/30"
                           >
                             <GiftIcon className="w-4 h-4 mr-1" />
                             Atualizar Gift Card
                           </Button>
                         )}
                       </>
                     )}
                    
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-6">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPendingOrders(currentPage - 1)}
              disabled={currentPage === 1}
              className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
            >
              Anterior
            </Button>
            
            <span className="text-blue-300 text-sm">
              Página {currentPage} de {totalPages} ({totalItems} itens)
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchPendingOrders(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="border-blue-500/30 text-blue-300 hover:bg-blue-950/30"
            >
              Próxima
            </Button>
          </div>
        )}
      </div>

      {/* Gift Card Update Modal */}
      <UpdateGiftCardModal
        isOpen={giftCardModalOpen}
        onClose={closeGiftCardModal}
        order={selectedOrder}
        onUpdate={handleGiftCardUpdate}
      />
    </Card>
  )
}
