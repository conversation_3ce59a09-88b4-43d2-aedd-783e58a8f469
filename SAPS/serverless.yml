service: saps-service

frameworkVersion: "3"

plugins:
  - serverless-export-env
  - serverless-webpack
  - serverless-prune-plugin
  - serverless-iam-roles-per-function
  - serverless-step-functions
  - serverless-plugin-warmup
  - serverless-plugin-split-stacks

custom:
  serverless-iam-roles-per-function:
    defaultInherit: true
  cronEnable:
    dev: true
    staging: true
    production: true
  stage: ${opt:stage, 'dev'}
  service: 'saps-service'
  awsId: "************"
  warmupEnable:
    dev: true
    staging: true
    production: true
  webpack:
    webpackConfig: webpack.config.js
    includeModules: false # Don't include node_modules in the bundle
    packager: 'npm'
    packagerOptions:
      ignoreScripts: true
    excludeFiles: '**/*.test.js'
    keepOutputDirectory: false
  # domainName:
  #   dev: ${self:service}-${self:custom.stage}.atomize.io
  #   staging: ${self:service}-${self:custom.stage}.atomize.io
  #   production: ${self:service}.atomize.io
  # customDomain:
  #   rest:
  #     domainName: ${self:custom.domainName.${self:custom.stage}}
  #     stage: ${self:custom.stage}
  #     certificateName: "*.atomize.io"
  #     createRoute53Record: false

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-2
  profile: atomize-dev
  stage: ${opt:stage, 'dev'}
  environment:
    BASE_SQS_QUEUE_NAME: ${self:service}-${self:custom.stage}-queue-
    BASE_SQS_QUEUE_ARN: arn:aws:sqs:${self:provider.region}:${self:custom.awsId}:${self:service}-${self:custom.stage}-queue-
    BASE_SQS_QUEUE_URL: https://sqs.${self:provider.region}.amazonaws.com/${self:custom.awsId}/${self:service}-${self:custom.stage}-queue-
    BASE_STATE_MACHINE_ARN: arn:aws:states:${self:provider.region}:${self:custom.awsId}:stateMachine:${self:service}-${self:custom.stage}-state-machine-
    BASE_STATE_MACHINE_NAME: ${self:service}-${self:custom.stage}-state-machine-
    BASE_LAMBDA_ARN: arn:aws:lambda:${self:provider.region}:${self:custom.awsId}:function:${self:service}-${self:custom.stage}- 
    STAGE: ${self:custom.stage}
    SERVICE: saps-service
    AWS_ACCOUNT_ID: "************"
    DATABASE_URL: ${ssm:/saps-${self:custom.stage}-database-url}
    REGION: us-east-2
    # S3_SECRET_ACCESS_KEY: ${ssm:/s3-${self:custom.stage}-secret-access-key}
    # S3_ACCESS_KEY_ID: ${ssm:/s3-${self:custom.stage}-access-key-id}
    # S3_BUCKET_NAME: ${ssm:/saps-${self:custom.stage}-bucket-name}
    # S3_ENDPOINT: ${ssm:/s3-${self:custom.stage}-endpoint}
    INVOICE_SNS_TOPIC: ${ssm:/matrixis-invoice-sns-topic}
    SNS_ACCESS_KEY_ID: ${ssm:/matrixis-sns-access-key-id}
    SNS_SECRET_ACCESS_KEY: ${ssm:/matrixis-sns-secret-access-key}
    # ATOMIZE_API_URL: ${ssm:/atomize-base-api-url}
    CDS_API_URL: ${ssm:/cds-${self:custom.stage}-api-url}
    NPM_ATOM_OLYMPIADS_TOKEN: ${ssm:/npm-atom-olympiads-token}
    JWT_SECRET: ${ssm:/${self:custom.stage}-service-jwt-secret}
    MATRIXIS_SQS_QUEUE_URL: https://sqs.${self:provider.region}.amazonaws.com/${self:custom.awsId}/matrixis-service-${self:custom.stage}-
    MATRIXIS_SQS_QUEUE_ARN: arn:aws:sqs:${self:provider.region}:${self:custom.awsId}:matrixis-service-${self:custom.stage}-queue
    
functions:
  - ${file(sls/video/lambda.yml)}
  - ${file(sls/playlist/lambda.yml)}
  - ${file(sls/teacher/lambda.yml)}
  - ${file(sls/middleware/lambda.yml)}

resources:
  - ${file(sls/resources/s3/bucket.yml)}

