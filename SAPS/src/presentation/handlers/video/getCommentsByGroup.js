import { getCommentsByGroupService } from "../../../application/services/video/getCommentsByGroupService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    console.log("getCommentsByGroup handler triggered with event:", event);
    
    const { groupId } = event.pathParameters;
    
    // Get query parameters
    const videoId = event.queryStringParameters?.videoId;
    const isDoubt = event.queryStringParameters?.isDoubt ? 
      event.queryStringParameters.isDoubt === 'true' : undefined;
    const page = event.queryStringParameters?.page ? 
      parseInt(event.queryStringParameters.page) : 1;
    const limit = event.queryStringParameters?.limit ? 
      parseInt(event.queryStringParameters.limit) : 20;

    if (!groupId) {
      return apiResponse(400, { body: { message: "groupId is required" } });
    }

    const result = await getCommentsByGroupService({
      groupId,
      videoId,
      isDoubt,
      page,
      limit,
      token: event.headers.Authorization
    });

    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getCommentsByGroup handler:', error);
    return apiResponse(error.statusCode || 500, { body: { message: error.message || "Internal server error" } });
  }
}
