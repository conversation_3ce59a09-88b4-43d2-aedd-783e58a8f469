import getVideoWithStatsService from "../../../application/services/video/getVideoWithStatsService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const { id } = event.pathParameters;
    const { videoId, playlistId } = event.queryStringParameters || {};
    console.log("ATHANDLER",{id, videoId, playlistId})

    const result = await getVideoWithStatsService({ 
      userId: id, 
      videoId: parseInt(videoId), 
      playlistId: parseInt(playlistId),
      token: event.headers.Authorization
    });
    console.log("RESULT", result)
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log('Error in getVideoWithStats handler:', error);
    return apiResponse(error.statusCode || 500, { body: error });
  }
} 