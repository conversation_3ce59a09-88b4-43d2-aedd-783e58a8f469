datasource db {
  provider = "postgresql" // Set the provider to 'postgresql'
  url      = env("DATABASE_URL") // Use the DATABASE_URL from your .env file
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native"]
  output        = "../generated/client"
}

// Define your data models here
model Video {
  id            Int             @id @default(autoincrement())
  title         String
  teacher       Teacher         @relation(fields: [teacherId], references: [id])
  description   String          @default("")
  teacherId     Int
  videoUrl      String
  comments      Comment[]       @relation("VideoComments")
  likes         VideoLike[]
  subject       String
  savedBy       VideoSaved[]
  watchedBy     VideoWatched[]
  levelRange    Int[]
  tags          String[]
  thumbnail     String?
  videoFiles    String[]
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  PlaylistVideo PlaylistVideo[]
}

model VideoLike {
  id      Int    @id @default(autoincrement())
  userId  String
  video   Video  @relation(fields: [videoId], references: [id], onDelete: Cascade)
  videoId Int

  createdAt DateTime @default(now())

  @@unique([videoId, userId])
}

model VideoSaved {
  id        Int      @id @default(autoincrement())
  userId    String
  videoId   Int
  video     Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([videoId, userId])
}

model VideoWatched {
  id        Int      @id @default(autoincrement())
  userId    String
  videoId   Int
  watchedAt DateTime @default(now())
  video     Video    @relation(fields: [videoId], references: [id])

  @@unique([videoId, userId])
}

model Teacher {
  id          Int      @id @default(autoincrement())
  name        String
  userId      String?
  awards      String[]
  videos      Video[]
  description String   @default("")
  createdAt   DateTime @default(now())
}

model Comment {
  id               Int           @id @default(autoincrement())
  userId           String
  username         String        @default("Anonymous")
  content          String
  isDoubt          Boolean       @default(false)
  responseComments Comment[]     @relation("CommentResponses")
  parentComment    Comment?      @relation("CommentResponses", fields: [parentCommentId], references: [id], onDelete: Cascade)
  parentCommentId  Int?
  video            Video?        @relation("VideoComments", fields: [videoId], references: [id], onDelete: Cascade)
  videoId          Int?
  likes            CommentLike[]
  createdAt        DateTime      @default(now())
}

model CommentLike {
  id        Int     @id @default(autoincrement())
  userId    String
  comment   Comment @relation(fields: [commentId], references: [id], onDelete: Cascade)
  commentId Int

  @@unique([commentId, userId])
}

enum PlaylistType {
  GLOBAL
  LOCAL
  PRIVATE
  LIKED
  SAVED
}

model Playlist {
  id            Int             @id @default(autoincrement())
  title         String
  subject       String[]
  olympiads     String[]
  levelRange    Int[]
  type          PlaylistType
  ownerId       String          @default("atomize") // It's either atomize or a user id
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  PlaylistVideo PlaylistVideo[]

  @@unique([title, ownerId])
}

model PlaylistVideo {
  playlistId Int
  videoId    Int
  order      Int

  playlist Playlist @relation(fields: [playlistId], references: [id], onDelete: Cascade)
  video    Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)

  @@id([playlistId, videoId])
}
