// VideoRepository.js
import { PrismaClient } from '../models/generated/client';
import VideoAggregateRoot from '../aggregates/videoAggregateRoot.js';
import { NotFoundError } from '../../application/customErrors/index.js';

const prisma = new PrismaClient();

class VideoRepository {
    async findById(videoId) {
        const videoData = await prisma.video.findUnique({
            where: { id: videoId },
            include: {
                comments: {
                    include: {
                        likes: true,
                        responseComments: {
                            include: {
                                likes: true
                            }
                        },
                        video:{
                            include: {
                                teacher: true
                            }
                        }
                    }
                },
                likes: true
            }
        });

        if (!videoData) {
            throw new NotFoundError("Video", `Video with id ${videoId} not found`);
        }

        return VideoAggregateRoot.fromData(videoData);
    }

    async findByIdWithMetrics({ videoId, userId = null }) {
        const videoData = await prisma.video.findUnique({
            where: { id: videoId },
            include: {
                comments: {
                    include: {
                        likes: userId ? { where: { userId: userId } } : true,
                        responseComments: {
                            include: {
                                likes: true
                            }
                        },
                        _count: { select: { likes: true, responseComments: true } },
                    }
                },
                savedBy: userId ? { where: { userId } } : true,
                likes: userId ? { where: { userId: userId } } : true,
                _count: { 
                    select: { 
                        likes: true, 
                        savedBy: true, 
                        comments: true,
                    } 
                },
            }
        });
        const teacher = await prisma.teacher.findUnique({
            where: { id: videoData.teacherId }
        })
        console.log("videoData", videoData)
    
        if (!videoData) {
            throw new NotFoundError("Video", `Video with id ${videoId} not found`);
        }
    
        const hasUserLiked = !!videoData.likes.length;
        const hasUserSaved = userId ? videoData.savedBy.some(save => save.userId === userId) : false;
    
        const userLikedCommentsIds = []
        videoData.comments.forEach(comment => {
            console.log(comment)
            if (comment.likes?.some(like => like.userId === userId)) {
                userLikedCommentsIds.push(comment.id)
            }
        });

        return {
            video: VideoAggregateRoot.fromData({...videoData, teacher}),
            statistics: {
                numberOfLikes: videoData._count.likes,
                numberOfSaves: videoData._count.savedBy,
                numberOfComments: videoData._count.comments,
            },
            userStatistics: userId ? {
                hasUserLiked,
                hasUserSaved,
                userLikedCommentsIds,
            } : null
        };
    }

    async findCommentById(commentId) {
        const commentData = await prisma.comment.findUnique({
            where: { id: commentId },
            include: {
                likes: true
            }
        })
        if (!commentData) {
            throw new NotFoundError("Comment", `Comment with id ${commentId} not found`);
        }
        return commentData;
    }

    async create(videoData) {
        const newVideo = await prisma.video.create({
            data: { ...videoData }
        });

        return newVideo;
    }

    async update(videoData) {
        const updatedVideo = await prisma.video.update({
            where: { id: videoData.id },
            data: { ...videoData }
        });

        return updatedVideo;
    }

    async addSave(saveData) {
        const newSaveData = await prisma.videoSaved.create({
            data: { ...saveData }
        });
        return newSaveData;
    }

    async addLike(likeData) {
        const newLikeData = await prisma.videoLike.create({
            data: { ...likeData }
        });
        return newLikeData;
    }

    async addComment(commentData) {
        const newComment = await prisma.comment.create({
            data: { ...commentData },
            include: {
                likes: true,
                responseComments: true
            }
        });
        return newComment;
    }

    async addCommentLike(commentLikeData) {
        const newLikeData = await prisma.commentLike.create({
            data: { ...commentLikeData }
        });
        return newLikeData;
    }

    async getLikedVideos(page = 1, limit = 10, userId) {
        const skip = (page - 1) * limit;
        const videosData = await prisma.video.findMany({
            skip,
            take: limit,
            where: {
                likes: {
                    some: {
                        userId: userId
                    }
                }
            }
        });

        return videosData.map(videoData => VideoAggregateRoot.fromData(videoData));
    }

    async getSavedVideos(page = 1, limit = 10, userId) {
        const skip = (page - 1) * limit;
        const videosData = await prisma.video.findMany({
            skip,
            take: limit,
            where: {
                savedBy: {
                    some: {
                        userId: userId
                    }
                }
            }
        });

        return videosData.map(videoData => VideoAggregateRoot.fromData(videoData));
    }



    async getPaginatedVideos(page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const videosData = await prisma.video.findMany({
            skip,
            take: limit,
            include: {
                comments: true,
                likes: true
            }
        });

        return videosData.map(videoData => VideoAggregateRoot.fromData(videoData));
    }

    async getWithFilters({ page = 1, limit = 10, filter }) {
        const skip = (page - 1) * limit;
        let videosData = []

        videosData = await prisma.video.findMany({
            skip,
            take: limit,
            where: filter
        });

        return videosData;
    }

    async deleteCommentLike({ userId, commentId }) {
        const deletedCommentLike = await prisma.commentLike.delete({
            where: {
                commentId_userId: { commentId, userId }
            }
        })
        return deletedCommentLike;
    }

    async deleteVideoLike({ userId, videoId }) {
        const deletedComment = await prisma.videoLike.delete({
            where: {
                videoId_userId: { videoId, userId }
            }
        })
        return deletedComment;
    }

    async deleteVideoSave({ userId, videoId }) {
        const deletedVideoSave = await prisma.videoSaved.delete({
            where: {
                videoId_userId: { videoId, userId }
            }
        })
        return deletedVideoSave;
    }

    async deleteComment(commentId) {
        const deletedComment = await prisma.comment.delete({
            where: { id: commentId }
        })
        return deletedComment;
    }

    async delete(videoId) {

        await prisma.$transaction(
            [
                prisma.video.delete({ where: { id: videoId } }),
                prisma.videoLike.deleteMany({ where: { videoId: videoId } }),
                prisma.videoSaved.deleteMany({ where: { videoId: videoId } }),
            ]
        ).catch(
            err => { throw err }
        )

        return true;
    }
}

export default VideoRepository;