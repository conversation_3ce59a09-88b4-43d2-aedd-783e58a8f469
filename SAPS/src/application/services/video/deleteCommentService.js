import VideoRepository from "../../../domain/repositories/videoRepository";

const videoRepository = new VideoRepository();
export default async function deleteCommentService(commentId) {
    try {
        const deletedComment = await videoRepository.deleteComment(commentId);
        
        const commentId = deletedComment.id;
        const videoId = deletedComment.videoId;
        const userId = deletedComment.userId;
        const isDoubt = deletedComment.isDoubt;

        await CdsApi.addOrRemoveCommentFromUser({ userId, videoId, commentId, isDoubt, isRemove: true });
        return deletedComment;
    } catch (error) {
        console.error(`Error deleting comment: ${error.message}`);
        throw error;
    }
}