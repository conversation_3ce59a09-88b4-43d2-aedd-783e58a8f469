import VideoRepository from '../../../domain/repositories/videoRepository.js';
import { NotFoundError } from '../../customErrors/index.js';
import CdsApi from '../../../infra/ATOMIZE/CDS/CdsApi.js';

const videoRepository = new VideoRepository();

export async function addCommentToVideoService({ videoId, content, isDoubt, parentCommentId = null, userId, username }) {
    try {
        const video = await videoRepository.findById(videoId);
        let newCommentData = null;
        
        if (parentCommentId) {
            const comments = await video.getComments();
            const parentComment = comments.find(comment => comment.id === parentCommentId);
            if (!parentComment) {
                throw new NotFoundError("Comment", `Comment with id ${parentCommentId} not found`);
            }
            newCommentData = await video.addCommentToComment({ 
                commentId: parentCommentId, 
                content, 
                userId,
                username 
            });
        } else {
            newCommentData = await video.addComment({ content, isDoubt, userId, username });
        }
        
        const newComment = await videoRepository.addComment(newCommentData);
        const commentId = newComment.id ? newComment.id : newComment.responseComments.id;
        await CdsApi.addOrRemoveCommentFromUser({ userId, videoId, commentId, isDoubt, isRemove: false });

        return newComment;
    } catch (error) {
        console.error(`Error creating comment: ${error.message}`);
        throw error;
    }
}