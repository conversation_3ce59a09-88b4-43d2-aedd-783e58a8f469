export class customError extends Error {
    status;
    name;
    constructor(name, message, status) {
        super(message);
        this.status = status;
        this.name = name;
    }
}

export class ValidationError extends customError {
    name = "Validation error";
    status = 400;
    message;
    field;
    constructor(field, message) {
        super(message);
        this.field = field;
        this.message = `Validation error on the ${this.field} field`;
        if (message) this.message += ", " + message;
    }
}

export class MissingInputsError extends customError {
    fields;
    constructor(fields) {
        const message = `Missing required inputs: ${fields.join(", ")}`;
        super("MissingInputErrors", message, 400);
        this.fields = fields;
    }
}

export class NotFoundError extends customError {
    name = "Not Found error";
    status = 404;
    resource;
    constructor(resource = "Resource", message = "Not Found") {
        super("NotFoundError", message, 404);
        this.resource = resource;
        this.message = `${this.resource} not found`;
        if (message) this.message += ": " + message;
    }
}

export class EnumValueError extends customError {
    name = "Enum value error";
    status = 400;
    field;
    allowedValues;
    constructor(field, value, allowedValues) {
        super(
            "EnumValueError",
            `Invalid value '${value}' for field '${field}'. Allowed values are: ${allowedValues.join(
                ", "
            )}.`,
            400
        );
        this.field = field;
        this.allowedValues = allowedValues;
    }
}
